# Dependencies
node_modules/
node_modules/*
npm-debug.log*
yarn-debug.log*
yarn-error.log*
pnpm-debug.log*
Ohio-Residential-Real-Estate-Purchase-Agreement.pdf
package-lock.json
notes.md
package-lock.json


# Next.js
.next/
out/
build/
dist/
reforms/
reforms/*
# Environment variables
.env
.env.local
.env.example
.env.development.local
.env.test.local
.env.production.local

API_PARAMETERS_COMPLETE.md
API_RESPONSES_COMPLETE.md

# Vercel
.vercel

# TypeScript
*.tsbuildinfo
next-env.d.ts

# IDE
.vscode/
.idea/
.history/
*.swp
*.swo

# OS
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Logs
logs
*.log

# Runtime data
pids
*.pid
*.seed
*.pid.lock

# Coverage directory used by tools like istanbul
coverage/
*.lcov

# nyc test coverage
.nyc_output

# Dependency directories
jspm_packages/

# Optional npm cache directory
.npm

# Optional eslint cache
.eslintcache

# Optional REPL history
.node_repl_history

# Output of 'npm pack'
*.tgz

# Yarn Integrity file
.yarn-integrity

# parcel-bundler cache (https://parceljs.org/)
.cache
.parcel-cache

# Prisma
prisma/migrations/
