# Real Estate API Complete Parameter Documentation

## Address Standardization APIs

### AutoComplete API
**Endpoint:** `POST https://api.realestateapi.com/v2/AutoComplete`

**Parameters for 200 Response:**
- `input` (object, required)
  - `search` (string, required): Partial address/location text
  - `search_types` (array, required): Search type codes ["A", "C", "G", "N", "P", "T", "Z"]

**Search Type Definitions:**
- "A": Full Address Completion
- "C": City Completion
- "G": Neighborhood Completion  
- "N": County Completion
- "P": APN Completion
- "T": State Completion
- "Z": Zip Completion

**Example Valid Requests:**
```json
{"input": {"search": "9 Topeka Pa", "search_types": ["A"]}}
{"input": {"search": "Herndon, V", "search_types": ["C"]}}
{"input": {"search": "Brunswick", "search_types": ["N"]}}
{"input": {"search": "22205", "search_types": ["Z"]}}
```

### Address Verification API
**Endpoint:** `POST https://api.realestateapi.com/v2/AddressVerification`

**Parameters for 200 Response:**
- `addresses` (array, required, max 100): Array of address objects
  - Each address can have:
    - `street` (string): Street address
    - `city` (string): City name
    - `state` (string): State abbreviation
    - `zip` (string): ZIP code
    - `key` (string, optional): Mapping identifier

**Minimum Required Combinations:**
1. `street` + `zip` (city/state optional)
2. `street` + `city` + `state`
3. Full address string in `street` field

## Mapping APIs

### Property Boundary API
**Endpoint:** `POST https://api.realestateapi.com/v1/PropertyParcel`

**Parameters for 200 Response (Choose ONE method):**

**Method 1 - By Address:**
- `address` (string, required): Full street address including number, street, city, state, ZIP

**Method 2 - By Coordinates:**
- `longitude` (float, required): Decimal longitude
- `latitude` (float, required): Decimal latitude
- `radius` (float, optional): Search radius around coordinates

**Method 3 - By APN+FIPS:**
- `fips` (string, required): County FIPS code
- `apn` (string, required): Assessor's Parcel Number

**Method 4 - By Polygon:**
- `polygon` (array, required): Array of coordinate pairs defining search area

### Mapping Pins API (BETA)
**Endpoint:** `POST https://api.realestateapi.com/v2/PropertyMapping`
**Requirement:** Growth+ Plans only
**Parameters:** Not fully documented - contact support for details

## Property APIs

### Property Detail API
**Endpoint:** `POST https://api.realestateapi.com/v2/PropertyDetail`

**Parameters for 200 Response (Choose ONE method):**

**Method 1 - By Property ID:**
- `id` (integer, required): Property identifier from Property Search

**Method 2 - By Address:**
- `address` (string, required): Full property address
- `city` (string, optional): City name
- `state` (string, optional): State abbreviation
- `zip` (string, optional): ZIP code

**Method 3 - By APN+FIPS:**
- `fips` (string, required): County FIPS code
- `apn` (string, required): Assessor's Parcel Number

**Optional Enhancement Parameters:**
- `include` (array, optional): Specific data sections to include
- `exclude` (array, optional): Data sections to exclude

### Property Search API
**Endpoint:** `POST https://api.realestateapi.com/v2/PropertySearch`

**Parameters for 200 Response:**

**Geographic Parameters (At least ONE required):**
- `address` (string): Street address
- `city` (string): City name
- `state` (string): State abbreviation
- `zip` (string): ZIP code
- `county` (string): County name
- `fips` (string): County FIPS code
- `latitude` (float): Latitude coordinate
- `longitude` (float): Longitude coordinate
- `radius` (float): Search radius in miles
- `polygon` (array): Polygon coordinates for boundary search

**Filter Parameters (All optional):**
- `bedrooms_min` (integer): Minimum bedrooms
- `bedrooms_max` (integer): Maximum bedrooms
- `bathrooms_min` (float): Minimum bathrooms
- `bathrooms_max` (float): Maximum bathrooms
- `square_feet_min` (integer): Minimum square footage
- `square_feet_max` (integer): Maximum square footage
- `lot_square_feet_min` (integer): Minimum lot size
- `lot_square_feet_max` (integer): Maximum lot size
- `year_built_min` (integer): Minimum year built
- `year_built_max` (integer): Maximum year built
- `estimated_value_min` (integer): Minimum estimated value
- `estimated_value_max` (integer): Maximum estimated value
- `last_sale_amount_min` (integer): Minimum last sale amount
- `last_sale_amount_max` (integer): Maximum last sale amount
- `property_type` (array): Property types ["CONDO", "SINGLE_FAMILY", etc.]
- `property_use` (array): Property use codes
- `absentee_owner` (boolean): Owner-occupied filter
- `owner_occupied` (boolean): Owner occupancy status
- `vacant` (boolean): Vacancy status
- `foreclosure` (boolean): Foreclosure status
- `equity_min` (integer): Minimum equity amount
- `equity_max` (integer): Maximum equity amount

**Control Parameters (All optional):**
- `limit` (integer, max 1000): Number of results to return
- `offset` (integer): Starting position for pagination
- `sort` (string): Sort field
- `sort_direction` (string): "asc" or "desc"
- `search_type` (string): "count", "ids_only", "summary", or default
- `exclude` (array): Fields to exclude from response

### Property Detail Bulk API
**Endpoint:** `POST https://api.realestateapi.com/v2/PropertyDetailBulk`

**Parameters for 200 Response:**
- `ids` (array, required): Array of property IDs (min 1, max 1000)
- `include` (array, optional): Specific data sections to include
- `exclude` (array, optional): Data sections to exclude

### CSV Generator API
**Endpoint:** `POST https://api.realestateapi.com/v2/CSVBuilder`
**Parameters:** Not fully documented - use "Try It!" feature for details

### Involuntary Liens API
**Endpoint:** `POST https://api.realestateapi.com/v2/Reports/PropertyLiens`

**Parameters for 200 Response:**
- `id` (integer, required): Property ID
- OR
- `address` (string, required): Full property address
- `city` (string, optional): City name  
- `state` (string, optional): State abbreviation
- `zip` (string, optional): ZIP code

## Valuation APIs

### Property Comps v2 API
**Endpoint:** `POST https://api.realestateapi.com/v2/PropertyComps`

**Parameters for 200 Response:**
- `id` (integer, required): Subject property ID
- OR
- `address` (string, required): Subject property address
- `city` (string, optional): City name
- `state` (string, optional): State abbreviation
- `zip` (string, optional): ZIP code

**Optional Parameters:**
- `radius` (float): Search radius for comps
- `count` (integer): Number of comps to return
- `days_back` (integer): How far back to search for sales

### Property Comps v3 API
**Endpoint:** `POST https://api.realestateapi.com/v3/PropertyComps`

**Parameters for 200 Response:**
- `subject` (object, required): Subject property details
- `comps_settings` (object, optional): Custom comp definitions
- `boosts` (object, optional): Weighting adjustments

### Lender Grade AVM API
**Endpoint:** `POST https://api.realestateapi.com/v2/PropertyAvm`

**Parameters for 200 Response:**
- `id` (integer, required): Property ID
- OR
- `address` (string, required): Property address
- `city` (string, optional): City name
- `state` (string, optional): State abbreviation
- `zip` (string, optional): ZIP code

## SkipTrace APIs

### SkipTrace API
**Endpoint:** `POST https://api.realestateapi.com/v1/SkipTrace`

**Parameters for 200 Response:**
**Required:**
- `address` (string, required): Street address
- `city` (string, required): City name
- `state` (string, required): State abbreviation
- `zip` (string, required): ZIP code

**Optional:**
- `first_name` (string): Owner's first name
- `last_name` (string): Owner's last name

### Bulk SkipTrace API
**Endpoint:** `POST https://api.realestateapi.com/v1/SkipTraceBatch`

**Parameters for 200 Response:**
- `requests` (array, required): Array of skiptrace requests (min 10, max 1000)
- `webhook_url` (string, required): HTTPS endpoint for individual results
- `webcomplete_url` (string, optional): Endpoint for batch completion

**Each request object requires:**
- `key` (string/integer, required): Unique identifier
- `address` (string, required): Street address
- `city` (string, required): City name
- `state` (string, required): State abbreviation
- `zip` (string, required): ZIP code
- `first_name` (string, optional): Owner's first name
- `last_name` (string, optional): Owner's last name

## AI APIs

### PropGPT API
**Endpoint:** `POST https://api.realestateapi.com/v2/PropGPT`

**Parameters for 200 Response:**
- `openai_api_key` (string, required): Valid OpenAI API key
- `query` (string, required): Natural language property query
- `model` (string, optional): OpenAI model selection

## Critical Success Factors:

### Single Parameter Success:
- **AutoComplete**: Only needs `input.search` + `input.search_types`
- **Address Verification**: Only needs `addresses` array
- **Property Search**: Only needs ONE geographic parameter (address, city+state, lat+lng, etc.)

### Multiple Parameter Requirements:
- **SkipTrace**: Must have complete address (address+city+state+zip)
- **Bulk SkipTrace**: Must have 10+ requests with webhook_url
- **PropGPT**: Must have openai_api_key + query

### Flexible Parameter APIs:
- **Property Detail**: Can use ID OR address OR apn+fips
- **Property Boundary**: Can use address OR coordinates OR apn+fips OR polygon
- **Property Comps**: Can use ID OR address details

All APIs require valid authentication headers with API key.