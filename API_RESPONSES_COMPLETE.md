# Real Estate API Complete Response Documentation

## Address Standardization APIs

### AutoComplete API Response
**Endpoint:** `POST https://api.realestateapi.com/v2/AutoComplete`

```json
{
  "data": [
    {
      "id": "integer",
      "apn": "string",
      "state": "string", 
      "zip": "string",
      "county": "string",
      "city": "string",
      "street": "string",
      "address": "string",
      "latitude": "float",
      "longitude": "float",
      "title": "string",
      "searchType": "string"
    }
  ],
  "totalResults": "integer",
  "returnedResults": "integer", 
  "statusCode": "integer",
  "statusMessage": "string",
  "credits": "integer",
  "requestExecutionTimeMS": "integer"
}
```

**Field Definitions:**
- `id`: Unique property identifier
- `apn`: Assessor's Parcel Number
- `state`: State abbreviation (e.g., "VA")
- `zip`: ZIP code
- `county`: County name
- `city`: City name
- `street`: Street name
- `address`: Full formatted address
- `latitude/longitude`: Geographic coordinates
- `title`: Display-ready location description
- `searchType`: Type of match ("A", "C", "G", "N", "P", "T", "Z")

### Address Verification API Response
**Endpoint:** `POST https://api.realestateapi.com/v2/AddressVerification`

```json
{
  "addresses": [
    {
      "id": "integer",
      "propertyId": "string",
      "vacant": "boolean",
      "absenteeOwner": "boolean",
      "apn": "string",
      "latitude": "float",
      "longitude": "float",
      "lotNumber": "string",
      "propertyUse": "string",
      "precision": "string",
      "searchType": "string",
      "match": "boolean",
      "confidence": "float",
      "address": {
        "fips": "string",
        "house": "string",
        "address": "string",
        "street": "string",
        "preDirection": "string",
        "streetType": "string",
        "unit": "string",
        "unitType": "string",
        "city": "string",
        "county": "string",
        "state": "string",
        "zip": "string",
        "zip4": "string",
        "carrierRoute": "string",
        "congressionalDistrict": "integer",
        "label": "string",
        "addressFormat": "string"
      },
      "mailAddress": {
        // Same structure as address
      }
    }
  ]
}
```

**Field Definitions:**
- `match`: Whether address was successfully verified
- `confidence`: Statistical match strength (>0.85 for good matches)
- `vacant`: Property vacancy status
- `absenteeOwner`: Whether owner lives elsewhere
- `precision`: Location accuracy level

## Mapping APIs

### Property Boundary API Response
**Endpoint:** `POST https://api.realestateapi.com/v1/PropertyParcel`

```json
{
  "type": "FeatureCollection",
  "features": [
    {
      "type": "Feature",
      "properties": {
        "id": "integer",
        "apn": "string",
        "fips": "string",
        "address": "string",
        "city": "string",
        "state": "string",
        "zip": "string",
        "county": "string"
      },
      "geometry": {
        "type": "Polygon",
        "coordinates": [
          [
            ["longitude", "latitude"],
            ["longitude", "latitude"]
          ]
        ]
      }
    }
  ]
}
```

**Field Definitions:**
- Returns standard GEOJSON format
- `coordinates`: Array of [longitude, latitude] points defining property boundary
- `properties`: Property identification and location data

### Mapping Pins API Response (BETA)
**Endpoint:** `POST https://api.realestateapi.com/v2/PropertyMapping`

```json
{
  "pins": [
    {
      "id": "integer",
      "latitude": "float", 
      "longitude": "float",
      "propertyType": "string",
      "estimatedValue": "integer",
      "address": "string"
    }
  ]
}
```

## Property APIs

### Property Detail API Response
**Endpoint:** `POST https://api.realestateapi.com/v2/PropertyDetail`

```json
{
  "id": "integer",
  "propertyId": "string",
  "address": "string",
  "city": "string", 
  "state": "string",
  "zip": "string",
  "county": "string",
  "latitude": "float",
  "longitude": "float",
  "propertyType": "string",
  "propertyUse": "string",
  "vacant": "boolean",
  "absenteeOwner": "boolean",
  "ownerOccupied": "boolean",
  
  "comps": [
    {
      "id": "integer",
      "address": "string",
      "city": "string",
      "state": "string", 
      "zip": "string",
      "latitude": "float",
      "longitude": "float",
      "distance": "float",
      "bedrooms": "integer",
      "bathrooms": "float",
      "squareFeet": "integer",
      "lotSquareFeet": "integer",
      "yearBuilt": "integer",
      "lastSaleAmount": "integer",
      "lastSaleDate": "string",
      "propertyType": "string"
    }
  ],
  
  "auctionInfo": {
    "auctionDate": "string",
    "auctionType": "string",
    "trusteeAddress": "string",
    "originalLoanAmount": "integer",
    "unpaidBalance": "integer"
  },
  
  "currentMortgages": [
    {
      "lenderCode": "string",
      "lenderType": "string",
      "loanAmount": "integer",
      "interestRate": "float",
      "interestRateType": "string",
      "loanType": "string",
      "recordingDate": "string",
      "maturityDate": "string",
      "position": "string"
    }
  ],
  
  "demographics": {
    "medianHouseholdIncome": "integer",
    "medianAge": "float",
    "population": "integer",
    "averageHouseValue": "integer",
    "fairMarketRent": {
      "efficiency": "integer",
      "oneBedroom": "integer", 
      "twoBedroom": "integer",
      "threeBedroom": "integer",
      "fourBedroom": "integer"
    },
    "hudDesignations": ["string"]
  },
  
  "linkedProperties": [
    {
      "id": "integer",
      "address": "string",
      "propertyType": "string",
      "estimatedValue": "integer"
    }
  ],
  
  "lotInfo": {
    "apn": "string",
    "fips": "string",
    "subdivision": "string",
    "lotNumber": "string",
    "block": "string",
    "tract": "string",
    "landUse": "string",
    "zoning": "string",
    "legalDescription": "string",
    "lotSquareFeet": "integer",
    "acres": "float"
  },
  
  "mlsHistory": [
    {
      "listingId": "string",
      "listingDate": "string",
      "listingPrice": "integer",
      "listingStatus": "string",
      "daysOnMarket": "integer",
      "mlsHistoryType": "string",
      "agentName": "string",
      "officeName": "string"
    }
  ],
  
  "mortgageHistory": [
    {
      "recordingDate": "string",
      "lenderCode": "string",
      "lenderType": "string",
      "loanAmount": "integer",
      "interestRate": "float",
      "interestRateType": "string",
      "loanType": "string",
      "position": "string"
    }
  ],
  
  "neighborhood": {
    "neighborhoodId": "integer",
    "neighborhoodName": "string",
    "neighborhoodType": "string",
    "subdivisionName": "string"
  },
  
  "ownerInfo": {
    "owner1FirstName": "string",
    "owner1LastName": "string", 
    "owner2FirstName": "string",
    "owner2LastName": "string",
    "ownerType": "string",
    "yearsOwned": "integer",
    "estimatedEquity": "integer",
    "mailAddress": {
      "address": "string",
      "city": "string",
      "state": "string",
      "zip": "string",
      "addressFormat": "string"
    }
  },
  
  "propertyInfo": {
    "bedrooms": "integer",
    "bathrooms": "float",
    "squareFeet": "integer",
    "lotSquareFeet": "integer",
    "yearBuilt": "integer",
    "stories": "integer",
    "rooms": "integer",
    "units": "integer",
    "airConditioningType": "string",
    "basementType": "string",
    "constructionType": "string",
    "exteriorWalls": "string",
    "garageType": "string",
    "heatingType": "string",
    "heatingFuelType": "string",
    "interiorStructure": "string",
    "porchType": "string", 
    "roofMaterial": "string",
    "roofConstruction": "string",
    "fireplace": "boolean",
    "pool": "boolean"
  },
  
  "saleHistory": [
    {
      "saleDate": "string",
      "saleAmount": "integer",
      "purchaseMethod": "string",
      "transactionType": "string",
      "documentType": "string"
    }
  ],
  
  "schools": [
    {
      "schoolName": "string",
      "schoolType": "string",
      "gradeRange": "string",
      "districtName": "string",
      "rating": "integer",
      "distance": "float"
    }
  ],
  
  "taxInfo": {
    "assessedValue": "integer",
    "marketValue": "integer", 
    "taxAmount": "integer",
    "taxYear": "integer",
    "exemptions": ["string"]
  }
}
```

### Property Search API Response
**Endpoint:** `POST https://api.realestateapi.com/v2/PropertySearch`

```json
{
  "data": [
    {
      "id": "integer",
      "propertyId": "string",
      "address": "string",
      "city": "string",
      "state": "string", 
      "zip": "string",
      "county": "string",
      "latitude": "float",
      "longitude": "float",
      "propertyType": "string",
      "propertyUse": "string",
      "bedrooms": "integer",
      "bathrooms": "float",
      "squareFeet": "integer",
      "lotSquareFeet": "integer",
      "yearBuilt": "integer",
      "estimatedValue": "integer",
      "assessedValue": "integer",
      "lastSaleAmount": "integer",
      "lastSaleDate": "string",
      "owner1FirstName": "string",
      "owner1LastName": "string",
      "ownerOccupied": "boolean",
      "absenteeOwner": "boolean",
      "vacant": "boolean",
      "yearsOwned": "integer",
      "estimatedEquity": "integer"
    }
  ],
  "totalResults": "integer",
  "returnedResults": "integer",
  "statusCode": "integer", 
  "statusMessage": "string",
  "credits": "integer",
  "requestExecutionTimeMS": "integer"
}
```

### Property Detail Bulk API Response
**Endpoint:** `POST https://api.realestateapi.com/v2/PropertyDetailBulk`

```json
{
  "properties": [
    // Array of Property Detail objects (same structure as Property Detail API)
  ],
  "totalRequested": "integer",
  "totalReturned": "integer",
  "credits": "integer",
  "requestExecutionTimeMS": "integer"
}
```

### CSV Generator API Response
**Endpoint:** `POST https://api.realestateapi.com/v2/CSVBuilder`

```json
{
  "downloadUrl": "string",
  "fileName": "string",
  "recordCount": "integer",
  "fileSize": "integer",
  "expirationDate": "string"
}
```

### Involuntary Liens API Response
**Endpoint:** `POST https://api.realestateapi.com/v2/Reports/PropertyLiens`

```json
{
  "id": "integer",
  "propertyId": "string",
  "address": "string",
  "liens": [
    {
      "lienType": "string",
      "lienAmount": "integer",
      "lienDate": "string",
      "lienHolder": "string",
      "documentNumber": "string",
      "status": "string"
    }
  ],
  "totalLienAmount": "integer",
  "lienCount": "integer"
}
```

## Valuation APIs

### Property Comps v2 API Response
**Endpoint:** `POST https://api.realestateapi.com/v2/PropertyComps`

```json
{
  "subject": {
    "id": "integer",
    "address": "string",
    "city": "string",
    "state": "string",
    "zip": "string",
    "bedrooms": "integer",
    "bathrooms": "float", 
    "squareFeet": "integer",
    "lotSquareFeet": "integer",
    "yearBuilt": "integer",
    "propertyType": "string"
  },
  "comps": [
    {
      "id": "integer",
      "address": "string",
      "city": "string",
      "state": "string",
      "zip": "string",
      "distance": "float",
      "bedrooms": "integer",
      "bathrooms": "float",
      "squareFeet": "integer", 
      "lotSquareFeet": "integer",
      "yearBuilt": "integer",
      "lastSaleAmount": "integer",
      "lastSaleDate": "string",
      "propertyType": "string",
      "similarity": "float"
    }
  ],
  "compCount": "integer"
}
```

### Property Comps v3 API Response
**Endpoint:** `POST https://api.realestateapi.com/v3/PropertyComps`

```json
{
  "subject": {
    // Same as v2 subject structure
  },
  "comps": [
    {
      // Same as v2 comps structure plus:
      "adjustments": {
        "squareFootageAdjustment": "integer",
        "bedroomAdjustment": "integer", 
        "bathroomAdjustment": "integer",
        "lotSizeAdjustment": "integer",
        "ageAdjustment": "integer",
        "totalAdjustment": "integer"
      },
      "adjustedValue": "integer",
      "weight": "float"
    }
  ],
  "estimatedValue": "integer",
  "confidenceScore": "float",
  "compCount": "integer"
}
```

### Lender Grade AVM API Response
**Endpoint:** `POST https://api.realestateapi.com/v2/PropertyAvm`

```json
{
  "id": "integer",
  "propertyId": "string", 
  "address": "string",
  "estimatedValue": "integer",
  "valuationRange": {
    "low": "integer",
    "high": "integer"
  },
  "confidenceScore": "float",
  "models": [
    {
      "modelName": "string",
      "modelValue": "integer",
      "modelConfidence": "float"
    }
  ],
  "marketCondition": "string",
  "lastSaleDate": "string",
  "lastSaleAmount": "integer"
}
```

## SkipTrace APIs

### SkipTrace API Response
**Endpoint:** `POST https://api.realestateapi.com/v1/SkipTrace`

```json
{
  "identity": {
    "firstName": "string",
    "lastName": "string", 
    "middleName": "string",
    "age": "integer",
    "birthDate": "string",
    "addresses": [
      {
        "address": "string",
        "city": "string",
        "state": "string",
        "zip": "string",
        "type": "string",
        "dates": "string"
      }
    ],
    "phones": [
      {
        "number": "string",
        "type": "string",
        "carrier": "string"
      }
    ],
    "emails": [
      {
        "email": "string",
        "type": "string"
      }
    ]
  },
  "demographics": {
    "age": "integer",
    "gender": "string",
    "birthDate": "string",
    "education": "string",
    "jobs": [
      {
        "company": "string",
        "position": "string",
        "industry": "string"
      }
    ]
  },
  "stats": {
    "phonesFound": "integer",
    "emailsFound": "integer", 
    "jobsFound": "integer",
    "relationshipsFound": "integer"
  }
}
```

### Bulk SkipTrace API Response
**Endpoint:** `POST https://api.realestateapi.com/v1/SkipTraceBatch`

```json
{
  "batchId": "string",
  "receiveCount": "integer",
  "batchRequestIds": [
    {
      "key": "string",
      "batchRequestId": "string"
    }
  ]
}
```

**Individual Webhook Response (sent to webhook_url):**
```json
{
  "batchId": "string",
  "batchRequestId": "string", 
  "key": "string",
  "result": {
    // Same structure as SkipTrace API response
  }
}
```

## AI APIs

### PropGPT API Response
**Endpoint:** `POST https://api.realestateapi.com/v2/PropGPT`

```json
{
  "response": "string",
  "properties": [
    {
      "id": "integer",
      "address": "string",
      "reasoning": "string",
      "confidence": "float"
    }
  ],
  "tokensUsed": "integer",
  "model": "string"
}
```

**Field Definitions:**
- `response`: Natural language answer from AI
- `properties`: Relevant properties found
- `reasoning`: AI explanation for property selection
- `tokensUsed`: OpenAI tokens consumed
- `model`: OpenAI model used

## Universal Response Fields

All API responses include these common fields:
- `statusCode`: HTTP status code
- `statusMessage`: Status description  
- `credits`: API credits consumed
- `requestExecutionTimeMS`: Response time in milliseconds

## Error Response Structure

```json
{
  "error": "string",
  "statusCode": "integer",
  "statusMessage": "string",
  "details": "string"
}
```

**Common Error Codes:**
- `400`: Bad Request (invalid parameters)
- `401`: Unauthorized (invalid API key)
- `404`: Not Found (property/address not found)
- `429`: Too Many Requests (rate limit exceeded)
- `500`: Internal Server Error