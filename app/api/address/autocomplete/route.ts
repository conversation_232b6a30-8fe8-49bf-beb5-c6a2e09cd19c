import { NextRequest, NextResponse } from 'next/server';
import { searchAddress } from '@/app/lib/realestateapi';

export async function POST(request: NextRequest) {
  try {
    const { query } = await request.json();

    if (!query || query.length < 3) {
      return NextResponse.json({ results: [] });
    }

    const results = await searchAddress(query);
    console.log('Mapped results for frontend:', results);

    return NextResponse.json({ results });
  } catch (error) {
    console.error('Autocomplete API error:', error);
    return NextResponse.json(
      { error: 'Failed to search addresses' },
      { status: 500 }
    );
  }
}