import { NextRequest, NextResponse } from 'next/server';
import { PrismaClient } from '@prisma/client';
import { jwtVerify } from 'jose';
import { z } from 'zod';

const prisma = new PrismaClient();
const secret = new TextEncoder().encode(process.env.JWT_SECRET || 'fallback-secret');

const updateAgreementSchema = z.object({
  propertyDetails: z.object({}).passthrough().optional(),
  buyerInfo: z.object({}).passthrough().optional(),
  offerDetails: z.object({}).passthrough().optional(),
  forms: z.array(z.object({}).passthrough()).optional(),
  status: z.enum(['DRAFT', 'COMPLETED', 'EXPIRED']).optional(),
  pdfUrl: z.string().optional(),
});

async function getUserFromToken(request: NextRequest) {
  try {
    const token = request.cookies.get('auth-token')?.value;
    if (!token) return null;

    const { payload } = await jwtVerify(token, secret);
    return payload.userId as string;
  } catch {
    return null;
  }
}

// GET /api/agreements/[id] - Get specific agreement
export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const userId = await getUserFromToken(request);
    const agreementId = params.id;

    const agreement = await prisma.agreement.findUnique({
      where: { id: agreementId },
    });

    if (!agreement) {
      return NextResponse.json(
        { error: 'Agreement not found' },
        { status: 404 }
      );
    }

    // Check ownership for non-free agreements
    if (agreement.type !== 'PARTIAL_FREE' && agreement.userId !== userId) {
      return NextResponse.json(
        { error: 'Access denied' },
        { status: 403 }
      );
    }

    return NextResponse.json({ agreement });
  } catch (error) {
    console.error('Error fetching agreement:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  } finally {
    await prisma.$disconnect();
  }
}

// PUT /api/agreements/[id] - Update agreement
export async function PUT(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const userId = await getUserFromToken(request);
    const agreementId = params.id;
    const body = await request.json();
    const data = updateAgreementSchema.parse(body);

    // Find existing agreement
    const existingAgreement = await prisma.agreement.findUnique({
      where: { id: agreementId },
    });

    if (!existingAgreement) {
      return NextResponse.json(
        { error: 'Agreement not found' },
        { status: 404 }
      );
    }

    // Check ownership for non-free agreements
    if (existingAgreement.type !== 'PARTIAL_FREE' && existingAgreement.userId !== userId) {
      return NextResponse.json(
        { error: 'Access denied' },
        { status: 403 }
      );
    }

    // Update agreement
    const updatedAgreement = await prisma.agreement.update({
      where: { id: agreementId },
      data,
    });

    return NextResponse.json({ agreement: updatedAgreement });
  } catch (error) {
    console.error('Error updating agreement:', error);
    
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { error: error.errors[0].message },
        { status: 400 }
      );
    }

    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  } finally {
    await prisma.$disconnect();
  }
}

// DELETE /api/agreements/[id] - Delete agreement
export async function DELETE(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const userId = await getUserFromToken(request);
    const agreementId = params.id;

    // Find existing agreement
    const existingAgreement = await prisma.agreement.findUnique({
      where: { id: agreementId },
    });

    if (!existingAgreement) {
      return NextResponse.json(
        { error: 'Agreement not found' },
        { status: 404 }
      );
    }

    // Check ownership
    if (existingAgreement.userId !== userId) {
      return NextResponse.json(
        { error: 'Access denied' },
        { status: 403 }
      );
    }

    // Delete agreement
    await prisma.agreement.delete({
      where: { id: agreementId },
    });

    return NextResponse.json({ message: 'Agreement deleted successfully' });
  } catch (error) {
    console.error('Error deleting agreement:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  } finally {
    await prisma.$disconnect();
  }
}
