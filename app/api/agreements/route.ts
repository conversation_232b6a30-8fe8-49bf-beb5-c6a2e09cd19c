import { NextRequest, NextResponse } from 'next/server';
import { PrismaClient } from '@prisma/client';
import { jwtVerify } from 'jose';
import { z } from 'zod';

const prisma = new PrismaClient();
const secret = new TextEncoder().encode(process.env.JWT_SECRET || 'fallback-secret');

const createAgreementSchema = z.object({
  type: z.enum(['PARTIAL_FREE', 'PARTIAL_PAID', 'OFFICIAL']),
  propertyAddress: z.string().min(1, 'Property address is required'),
  propertyDetails: z.object({}).passthrough(),
  buyerInfo: z.object({}).passthrough(),
  offerDetails: z.object({}).passthrough(),
  forms: z.array(z.object({}).passthrough()).optional(),
});

async function getUserFromToken(request: NextRequest) {
  try {
    const token = request.cookies.get('auth-token')?.value;
    if (!token) return null;

    const { payload } = await jwtVerify(token, secret);
    return payload.userId as string;
  } catch {
    return null;
  }
}

// GET /api/agreements - Get user's agreements
export async function GET(request: NextRequest) {
  try {
    const userId = await getUserFromToken(request);
    
    if (!userId) {
      return NextResponse.json(
        { error: 'Authentication required' },
        { status: 401 }
      );
    }

    const agreements = await prisma.agreement.findMany({
      where: { userId },
      orderBy: { createdAt: 'desc' },
      select: {
        id: true,
        type: true,
        propertyAddress: true,
        status: true,
        createdAt: true,
        updatedAt: true,
      },
    });

    return NextResponse.json({ agreements });
  } catch (error) {
    console.error('Error fetching agreements:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  } finally {
    await prisma.$disconnect();
  }
}

// POST /api/agreements - Create new agreement
export async function POST(request: NextRequest) {
  try {
    const userId = await getUserFromToken(request);
    const body = await request.json();
    const data = createAgreementSchema.parse(body);

    // For free agreements, no authentication required
    if (data.type === 'PARTIAL_FREE') {
      const agreement = await prisma.agreement.create({
        data: {
          ...data,
          forms: data.forms || [],
          status: 'DRAFT',
        },
      });

      return NextResponse.json({ agreement });
    }

    // For paid agreements, authentication required
    if (!userId) {
      return NextResponse.json(
        { error: 'Authentication required for paid agreements' },
        { status: 401 }
      );
    }

    const agreement = await prisma.agreement.create({
      data: {
        ...data,
        userId,
        forms: data.forms || [],
        status: 'DRAFT',
      },
    });

    return NextResponse.json({ agreement });
  } catch (error) {
    console.error('Error creating agreement:', error);
    
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { error: error.errors[0].message },
        { status: 400 }
      );
    }

    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  } finally {
    await prisma.$disconnect();
  }
}
