import { NextRequest, NextResponse } from 'next/server';
import <PERSON><PERSON> from 'stripe';
import { z } from 'zod';
import { isDeveloperModeEnabled } from '@/app/lib/forms';

const stripe = new Stripe(process.env.STRIPE_SECRET_KEY!, {
  apiVersion: '2024-06-20',
});

const checkoutSchema = z.object({
  amount: z.number().min(1, 'Amount must be greater than 0'),
  email: z.string().email('Invalid email address'),
  agreementType: z.enum(['PARTIAL_PAID', 'OFFICIAL']),
  description: z.string().optional(),
});

export async function POST(request: NextRequest) {
  try {
    // Check if developer mode is enabled - bypass payment
    if (isDeveloperModeEnabled()) {
      return NextResponse.json({
        sessionId: 'dev_bypass_session',
        url: '/success?dev=true',
        message: 'Developer mode: Payment bypassed',
      });
    }

    const body = await request.json();
    const { amount, email, agreementType, description } = checkoutSchema.parse(body);

    // Create Stripe checkout session
    const session = await stripe.checkout.sessions.create({
      payment_method_types: ['card'],
      line_items: [
        {
          price_data: {
            currency: 'usd',
            product_data: {
              name: agreementType === 'OFFICIAL' 
                ? 'Complete Real Estate Agreement' 
                : 'Partial Real Estate Agreement',
              description: description || 'Real Estate Purchase Agreement Generation',
            },
            unit_amount: amount * 100, // Convert to cents
          },
          quantity: 1,
        },
      ],
      mode: 'payment',
      customer_email: email,
      success_url: `${request.nextUrl.origin}/success?session_id={CHECKOUT_SESSION_ID}`,
      cancel_url: `${request.nextUrl.origin}/cancel`,
      metadata: {
        agreementType,
        email,
      },
    });

    return NextResponse.json({
      sessionId: session.id,
      url: session.url,
    });
  } catch (error) {
    console.error('Stripe checkout error:', error);
    
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { error: error.errors[0].message },
        { status: 400 }
      );
    }

    return NextResponse.json(
      { error: 'Failed to create checkout session' },
      { status: 500 }
    );
  }
}
