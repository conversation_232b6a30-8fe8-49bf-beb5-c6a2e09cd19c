import { NextRequest, NextResponse } from 'next/server';
import <PERSON><PERSON> from 'stripe';
import { PrismaClient } from '@prisma/client';

const stripe = new Stripe(process.env.STRIPE_SECRET_KEY!, {
  apiVersion: '2024-06-20',
});

const prisma = new PrismaClient();
const webhookSecret = process.env.STRIPE_WEBHOOK_SECRET!;

export async function POST(request: NextRequest) {
  try {
    const body = await request.text();
    const signature = request.headers.get('stripe-signature')!;

    let event: Stripe.Event;

    try {
      event = stripe.webhooks.constructEvent(body, signature, webhookSecret);
    } catch (err) {
      console.error('Webhook signature verification failed:', err);
      return NextResponse.json(
        { error: 'Invalid signature' },
        { status: 400 }
      );
    }

    // Handle the event
    switch (event.type) {
      case 'checkout.session.completed':
        const session = event.data.object as Stripe.Checkout.Session;
        
        // Process successful payment
        await handleSuccessfulPayment(session);
        break;

      case 'payment_intent.succeeded':
        const paymentIntent = event.data.object as Stripe.PaymentIntent;
        console.log('Payment succeeded:', paymentIntent.id);
        break;

      case 'payment_intent.payment_failed':
        const failedPayment = event.data.object as Stripe.PaymentIntent;
        console.log('Payment failed:', failedPayment.id);
        break;

      default:
        console.log(`Unhandled event type: ${event.type}`);
    }

    return NextResponse.json({ received: true });
  } catch (error) {
    console.error('Webhook error:', error);
    return NextResponse.json(
      { error: 'Webhook handler failed' },
      { status: 500 }
    );
  } finally {
    await prisma.$disconnect();
  }
}

async function handleSuccessfulPayment(session: Stripe.Checkout.Session) {
  try {
    const { agreementType, email } = session.metadata || {};
    
    if (!agreementType || !email) {
      console.error('Missing metadata in session:', session.id);
      return;
    }

    // Find or create user
    let user = await prisma.user.findUnique({
      where: { email },
    });

    if (!user) {
      // Create guest user for this transaction
      user = await prisma.user.create({
        data: {
          email,
          password: '', // Guest user, no password
          name: session.customer_details?.name || 'Guest User',
        },
      });
    }

    // Create agreement record
    await prisma.agreement.create({
      data: {
        userId: user.id,
        type: agreementType as any,
        propertyAddress: 'TBD', // Will be updated when form is completed
        propertyDetails: {},
        buyerInfo: {},
        offerDetails: {},
        forms: [],
        status: 'DRAFT',
      },
    });

    console.log('Payment processed successfully for:', email);
  } catch (error) {
    console.error('Error processing successful payment:', error);
  }
}
