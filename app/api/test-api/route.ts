import { NextRequest, NextResponse } from 'next/server';
import { testApiConnection } from '@/app/lib/realestateapi';

export async function GET(request: NextRequest) {
  try {
    const result = await testApiConnection();
    
    return NextResponse.json({
      ...result,
      timestamp: new Date().toISOString(),
      environment: {
        apiUrl: process.env.REAL_ESTATE_API_URL || 'Not set',
        apiKeyConfigured: !!process.env.REAL_ESTATE_API_KEY,
        nodeEnv: process.env.NODE_ENV,
      }
    });
  } catch (error) {
    console.error('Test API endpoint error:', error);
    return NextResponse.json(
      { 
        success: false, 
        message: 'Test endpoint failed',
        error: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    );
  }
}
