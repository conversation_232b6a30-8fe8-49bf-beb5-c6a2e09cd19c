'use client';

import { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { Bug, Settings, X, Database, FileText, CreditCard } from 'lucide-react';
import { isDevMode, setDevMode, isDeveloperModeEnabled, getMockFormData } from '@/app/lib/forms';

interface DeveloperToolsProps {
  onMockDataFill?: (data: any) => void;
  currentFormData?: any;
}

export default function DeveloperTools({ onMockDataFill, currentFormData }: DeveloperToolsProps) {
  const [isOpen, setIsOpen] = useState(false);
  const [devModeEnabled, setDevModeEnabled] = useState(false);

  useEffect(() => {
    // Check if dev mode is enabled on mount
    setDevModeEnabled(isDevMode());
  }, []);

  // Only show in development environment or when developer mode is enabled
  if (!isDeveloperModeEnabled() && process.env.NODE_ENV !== 'development' && !devModeEnabled) {
    return null;
  }

  const toggleDevMode = () => {
    const newState = !devModeEnabled;
    setDevModeEnabled(newState);
    setDevMode(newState);
    if (window) {
      window.location.reload();
    }
  };

  const fillMockData = () => {
    if (onMockDataFill) {
      const mockData = getMockFormData();
      onMockDataFill({
        ...mockData,
        agreementType: 'OFFICIAL',
        email: mockData.buyerInfo.email,
        devMode: true
      });
    }
  };

  const bypassPayment = () => {
    console.log('Payment bypassed in dev mode');
    // This would trigger the payment success flow
    if (window) {
      const event = new CustomEvent('dev-payment-bypass', { detail: { success: true } });
      window.dispatchEvent(event);
    }
  };

  const clearDatabase = async () => {
    if (confirm('Are you sure you want to clear all local data?')) {
      // Clear localStorage
      localStorage.clear();
      // Clear sessionStorage
      sessionStorage.clear();
      // Reload the page
      window.location.reload();
    }
  };

  return (
    <>
      {/* Floating Dev Tools Button */}
      <motion.button
        className="fixed bottom-4 right-4 z-50 bg-purple-600 text-white p-3 rounded-full shadow-lg hover:bg-purple-700"
        onClick={() => setIsOpen(!isOpen)}
        whileHover={{ scale: 1.1 }}
        whileTap={{ scale: 0.9 }}
      >
        <Bug className="w-6 h-6" />
      </motion.button>

      {/* Dev Tools Panel */}
      <AnimatePresence>
        {isOpen && (
          <motion.div
            initial={{ x: 400, opacity: 0 }}
            animate={{ x: 0, opacity: 1 }}
            exit={{ x: 400, opacity: 0 }}
            className="fixed top-0 right-0 h-full w-96 bg-gray-900 text-white shadow-2xl z-50 overflow-y-auto"
          >
            <div className="p-6">
              <div className="flex justify-between items-center mb-6">
                <h2 className="text-2xl font-bold flex items-center">
                  <Settings className="w-6 h-6 mr-2" />
                  Developer Tools
                </h2>
                <button
                  onClick={() => setIsOpen(false)}
                  className="text-gray-400 hover:text-white"
                >
                  <X className="w-6 h-6" />
                </button>
              </div>

              {/* Dev Mode Toggle */}
              <div className="mb-6 p-4 bg-gray-800 rounded-lg">
                <div className="flex items-center justify-between">
                  <span className="font-medium">Developer Mode</span>
                  <button
                    onClick={toggleDevMode}
                    className={`relative inline-flex h-6 w-11 items-center rounded-full transition-colors ${
                      devModeEnabled ? 'bg-green-600' : 'bg-gray-600'
                    }`}
                  >
                    <span
                      className={`inline-block h-4 w-4 transform rounded-full bg-white transition-transform ${
                        devModeEnabled ? 'translate-x-6' : 'translate-x-1'
                      }`}
                    />
                  </button>
                </div>
                <p className="text-sm text-gray-400 mt-2">
                  {devModeEnabled ? 'All features unlocked' : 'Enable to bypass restrictions'}
                </p>
              </div>

              {/* Quick Actions */}
              <div className="space-y-3">
                <h3 className="text-lg font-semibold mb-3">Quick Actions</h3>
                
                <button
                  onClick={fillMockData}
                  className="w-full bg-blue-600 hover:bg-blue-700 text-white py-3 px-4 rounded-lg flex items-center justify-center transition-colors"
                >
                  <FileText className="w-5 h-5 mr-2" />
                  Fill Mock Data
                </button>

                <button
                  onClick={bypassPayment}
                  className="w-full bg-green-600 hover:bg-green-700 text-white py-3 px-4 rounded-lg flex items-center justify-center transition-colors"
                  disabled={!devModeEnabled}
                >
                  <CreditCard className="w-5 h-5 mr-2" />
                  Bypass Payment
                </button>

                <button
                  onClick={clearDatabase}
                  className="w-full bg-red-600 hover:bg-red-700 text-white py-3 px-4 rounded-lg flex items-center justify-center transition-colors"
                >
                  <Database className="w-5 h-5 mr-2" />
                  Clear Local Data
                </button>
              </div>

              {/* Current Form Data */}
              {currentFormData && (
                <div className="mt-6">
                  <h3 className="text-lg font-semibold mb-3">Current Form Data</h3>
                  <div className="bg-gray-800 p-4 rounded-lg">
                    <pre className="text-xs overflow-x-auto">
                      {JSON.stringify(currentFormData, null, 2)}
                    </pre>
                  </div>
                </div>
              )}

              {/* Test States */}
              <div className="mt-6">
                <h3 className="text-lg font-semibold mb-3">Test Different States</h3>
                <div className="grid grid-cols-2 gap-2">
                  {['CA', 'TX', 'FL', 'NY', 'IL', 'PA'].map((state) => (
                    <button
                      key={state}
                      onClick={() => {
                        const mockData = {
                          propertyDetails: {
                            address: {
                              street: '123 Test St',
                              city: 'Test City',
                              state: state,
                              zipCode: '12345',
                              fullAddress: `123 Test St, Test City, ${state} 12345`
                            }
                          }
                        };
                        onMockDataFill?.(mockData);
                      }}
                      className="bg-gray-700 hover:bg-gray-600 py-2 px-3 rounded text-sm"
                    >
                      Test {state}
                    </button>
                  ))}
                </div>
              </div>

              {/* Environment Info */}
              <div className="mt-6 p-4 bg-gray-800 rounded-lg">
                <h3 className="text-lg font-semibold mb-2">Environment</h3>
                <div className="space-y-1 text-sm">
                  <p>Node Env: {process.env.NODE_ENV}</p>
                  <p>Dev Mode: {devModeEnabled ? 'Enabled' : 'Disabled'}</p>
                  <p>API URL: {process.env.NEXT_PUBLIC_API_URL || 'Not Set'}</p>
                </div>
              </div>
            </div>
          </motion.div>
        )}
      </AnimatePresence>
    </>
  );
}
