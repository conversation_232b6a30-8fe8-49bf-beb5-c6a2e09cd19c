'use client';

import { useState } from 'react';
import { motion } from 'framer-motion';
import { FileText, Shield, Clock, DollarSign } from 'lucide-react';
import AddressAutocomplete from './AddressAutocomplete';
import { AutoCompleteResult } from '@/app/types';

interface HeroSectionProps {
  onAddressSelect: (address: AutoCompleteResult) => void;
}

export default function HeroSection({ onAddressSelect }: HeroSectionProps) {
  const features = [
    {
      icon: FileText,
      title: 'Professional Forms',
      description: 'Access state-specific forms and documents',
    },
    {
      icon: Shield,
      title: 'Legally Compliant',
      description: 'Ensure your offers meet legal requirements',
    },
    {
      icon: Clock,
      title: 'Save Time',
      description: 'Generate agreements in minutes, not hours',
    },
    {
      icon: DollarSign,
      title: 'Affordable',
      description: 'Professional agreements without agent fees',
    },
  ];

  return (
    <div className="h-screen overflow-hidden bg-gradient-to-br from-slate-900 via-purple-900 to-slate-900 relative safe-area-padding">
      {/* Animated background elements */}
      <div className="absolute inset-0 overflow-hidden">
        <div className="absolute -inset-10 opacity-30 md:opacity-50">
          <div className="absolute top-1/4 left-1/4 w-48 h-48 md:w-72 md:h-72 bg-purple-500 rounded-full mix-blend-multiply filter blur-xl animate-pulse"></div>
          <div className="absolute top-1/3 right-1/4 w-48 h-48 md:w-72 md:h-72 bg-blue-500 rounded-full mix-blend-multiply filter blur-xl animate-pulse animation-delay-2000"></div>
          <div className="absolute bottom-1/4 left-1/3 w-48 h-48 md:w-72 md:h-72 bg-pink-500 rounded-full mix-blend-multiply filter blur-xl animate-pulse animation-delay-4000"></div>
        </div>
      </div>

      {/* Navbar */}
      <nav className="relative z-20 w-full">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-4">
          <motion.div
            initial={{ opacity: 0, y: -20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6 }}
            className="flex items-center justify-between"
          >
            <div className="flex items-center">
              <span className="w-2 h-2 bg-green-400 rounded-full mr-3 animate-pulse"></span>
              <span className="text-white font-semibold text-lg">Professional Real Estate Documents</span>
            </div>
            <div className="hidden md:flex items-center space-x-6">
              <span className="text-gray-300 text-sm">Trusted by 10,000+ investors</span>
            </div>
          </motion.div>
        </div>
      </nav>

      {/* Main content */}
      <div className="relative z-10 flex-1 flex flex-col justify-center">{/* Removed justify-between */}

        {/* Main content area */}
        <div className="px-4 sm:px-6 lg:px-8 py-12">
          <div className="w-full max-w-5xl mx-auto text-center">
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.8, delay: 0.2 }}
              className="space-y-8 md:space-y-10"
            >
              <h1 className="text-4xl sm:text-5xl md:text-6xl lg:text-7xl font-bold text-white leading-tight">
                <span className="block">Professional Purchase</span>
                <span className="block bg-gradient-to-r from-blue-400 via-purple-400 to-pink-400 bg-clip-text text-transparent">
                  Agreements
                </span>
                <span className="block text-3xl sm:text-4xl md:text-5xl lg:text-6xl text-gray-300 font-normal mt-2">
                  Without an Agent
                </span>
              </h1>

              {/* Enhanced description with highlights */}
              <div className="max-w-4xl mx-auto">
                <p className="text-xl sm:text-2xl md:text-3xl text-white font-semibold mb-4">
                  Create Legally Compliant As-Is Purchase Agreements in Minutes
                </p>
                <div className="bg-white/10 backdrop-blur-sm rounded-2xl p-6 md:p-8 border border-white/20">
                  <p className="text-lg md:text-xl text-gray-200 leading-relaxed mb-4">
                    Perfect for <span className="text-blue-400 font-semibold">Wholesalers</span>,
                    <span className="text-purple-400 font-semibold"> Section 8 Investors</span>, and
                    <span className="text-pink-400 font-semibold"> Direct Buyers</span>
                  </p>
                  <div className="grid grid-cols-1 md:grid-cols-3 gap-4 text-sm md:text-base text-gray-300">
                    <div className="flex items-center justify-center">
                      <span className="w-2 h-2 bg-green-400 rounded-full mr-2"></span>
                      State-Specific Forms
                    </div>
                    <div className="flex items-center justify-center">
                      <span className="w-2 h-2 bg-blue-400 rounded-full mr-2"></span>
                      Legally Compliant
                    </div>
                    <div className="flex items-center justify-center">
                      <span className="w-2 h-2 bg-purple-400 rounded-full mr-2"></span>
                      No Agent Required
                    </div>
                  </div>
                </div>
              </div>

              {/* Address input section */}
              <div className="w-full max-w-3xl mx-auto">
                <motion.div
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.8, delay: 0.4 }}
                  className="bg-gradient-to-r from-white/15 to-white/10 backdrop-blur-md p-8 md:p-10 rounded-3xl border border-white/30 shadow-2xl"
                >
                  <h2 className="text-2xl md:text-3xl font-bold text-white mb-3">
                    Get Started Now
                  </h2>
                  <p className="text-base md:text-lg text-gray-200 mb-8">
                    Enter a property address or paste a listing URL to begin creating your agreement
                  </p>
                  <div className="relative">
                    <AddressAutocomplete onSelect={onAddressSelect} />
                  </div>

                  {/* Quick stats */}
                  <div className="mt-6 grid grid-cols-3 gap-4 text-center">
                    <div>
                      <div className="text-2xl font-bold text-blue-400">50+</div>
                      <div className="text-xs text-gray-300">States Supported</div>
                    </div>
                    <div>
                      <div className="text-2xl font-bold text-purple-400">5min</div>
                      <div className="text-xs text-gray-300">Average Time</div>
                    </div>
                    <div>
                      <div className="text-2xl font-bold text-pink-400">100%</div>
                      <div className="text-xs text-gray-300">Legal Compliance</div>
                    </div>
                  </div>
                </motion.div>
              </div>
            </motion.div>
          </div>
        </div>

        {/* Features and Pricing section - bottom of screen */}
        <div className="flex-shrink-0 pb-8 px-4 sm:px-6 lg:px-8">
          <div className="max-w-7xl mx-auto space-y-8">
            {/* Features grid */}
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.8, delay: 0.6 }}
              className="grid grid-cols-2 md:grid-cols-4 gap-4 md:gap-6"
            >
              {features.map((feature, index) => (
                <motion.div
                  key={feature.title}
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.6, delay: 0.8 + index * 0.1 }}
                  className="bg-white/5 backdrop-blur-sm p-4 md:p-5 rounded-xl border border-white/10 text-center hover:bg-white/10 transition-all duration-300"
                >
                  <feature.icon className="w-8 h-8 md:w-10 md:h-10 text-blue-400 mb-3 mx-auto" />
                  <h3 className="text-sm md:text-base font-semibold text-white mb-2">
                    {feature.title}
                  </h3>
                  <p className="text-xs md:text-sm text-gray-300 leading-relaxed">{feature.description}</p>
                </motion.div>
              ))}
            </motion.div>

            {/* Enhanced Pricing Section */}
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.8, delay: 1.0 }}
              className="text-center"
            >
              <h3 className="text-2xl md:text-3xl font-bold text-white mb-6">Simple, Transparent Pricing</h3>
              <div className="grid grid-cols-1 md:grid-cols-3 gap-6 max-w-4xl mx-auto">
                {/* Free Plan */}
                <div className="bg-white/5 backdrop-blur-sm p-6 rounded-2xl border border-white/10 hover:bg-white/10 transition-all duration-300">
                  <div className="text-3xl font-bold text-white mb-2">Free</div>
                  <p className="text-gray-300 mb-4">Initial Agreement</p>
                  <ul className="text-sm text-gray-300 space-y-2 mb-6">
                    <li>• Purchase agreement only</li>
                    <li>• No seller name included</li>
                    <li>• Perfect for initial interest</li>
                    <li>• Basic state compliance</li>
                  </ul>
                  <button className="w-full py-2 px-4 bg-white/10 text-white rounded-lg hover:bg-white/20 transition-colors">
                    Get Started Free
                  </button>
                </div>

                {/* Paid Plan - Featured */}
                <div className="bg-gradient-to-b from-blue-500/20 to-purple-500/20 backdrop-blur-sm p-6 rounded-2xl border-2 border-blue-400/50 hover:border-blue-400 transition-all duration-300 transform hover:scale-105">
                  <div className="text-xs bg-blue-400 text-white px-3 py-1 rounded-full inline-block mb-3">Most Popular</div>
                  <div className="text-4xl font-bold text-white mb-2">$5</div>
                  <p className="text-gray-200 mb-4">Complete Agreement</p>
                  <ul className="text-sm text-gray-200 space-y-2 mb-6">
                    <li>• All required state forms</li>
                    <li>• Seller name included</li>
                    <li>• Ready to submit</li>
                    <li>• Full legal compliance</li>
                    <li>• Priority support</li>
                  </ul>
                  <button className="w-full py-3 px-4 bg-blue-500 text-white rounded-lg hover:bg-blue-600 transition-colors font-semibold">
                    Choose Complete
                  </button>
                </div>

                {/* Subscription Plan */}
                <div className="bg-white/5 backdrop-blur-sm p-6 rounded-2xl border border-white/10 hover:bg-white/10 transition-all duration-300">
                  <div className="text-3xl font-bold text-purple-400 mb-2">$15<span className="text-lg text-gray-300">/mo</span></div>
                  <p className="text-gray-300 mb-4">Unlimited Access</p>
                  <ul className="text-sm text-gray-300 space-y-2 mb-6">
                    <li>• Unlimited agreements</li>
                    <li>• All states supported</li>
                    <li>• Priority support</li>
                    <li>• Advanced features</li>
                    <li>• Cancel anytime</li>
                  </ul>
                  <button className="w-full py-2 px-4 bg-purple-500/80 text-white rounded-lg hover:bg-purple-500 transition-colors">
                    Start Subscription
                  </button>
                </div>
              </div>
            </motion.div>
          </div>
        </div>
      </div>
    </div>
  );
}