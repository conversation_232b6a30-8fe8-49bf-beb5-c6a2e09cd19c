'use client';

import { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import ProgressTracker from './ProgressTracker';
import AddressAutocomplete from './AddressAutocomplete';
import DeveloperTools from './DeveloperTools';
import { FormData, AutoCompleteResult, VerifiedAddress, RequiredForm } from '@/app/types';
import { propertyDetailsSchema, buyerInfoSchema, offerDetailsSchema } from '@/app/lib/validations';
import { getRequiredForms } from '@/app/lib/forms';
import { formatCurrency, formatCurrencyInput, parseCurrencyInput } from '@/app/lib/utils';
import { AlertCircle, CheckCircle2, ArrowRight, ArrowLeft, Loader2 } from 'lucide-react';

const slideVariants = {
  enter: (direction: number) => ({
    x: direction > 0 ? 1000 : -1000,
    opacity: 0,
  }),
  center: {
    x: 0,
    opacity: 1,
  },
  exit: (direction: number) => ({
    x: direction < 0 ? 1000 : -1000,
    opacity: 0,
  }),
};

interface MultiStepFormProps {
  initialData?: any;
  initialAddress?: AutoCompleteResult | null;
}

export default function MultiStepForm({ initialData, initialAddress }: MultiStepFormProps) {
  const [formData, setFormData] = useState<FormData>({ step: 1 });
  const [direction, setDirection] = useState(0);
  const [loading, setLoading] = useState(false);
  const [verifiedAddress, setVerifiedAddress] = useState<VerifiedAddress | null>(null);
  const [requiredForms, setRequiredForms] = useState<RequiredForm[]>([]);
  const [propertyDetailsLoaded, setPropertyDetailsLoaded] = useState(false);
  const [propertyDetailsError, setPropertyDetailsError] = useState<string | null>(null);
  const [isGenerating, setIsGenerating] = useState(false);

  // Handle initial address if provided
  useEffect(() => {
    if (initialAddress && !formData.propertyDetails?.address) {
      handleAddressSelect(initialAddress);
    }
  }, [initialAddress]);

  const goToStep = (newStep: number) => {
    setDirection(newStep > formData.step ? 1 : -1);
    setFormData({ ...formData, step: newStep });
  };

  const handleAddressSelect = async (address: AutoCompleteResult) => {
    console.log('Selected address:', address);

    // Set loading state and show property verification step
    setLoading(true);
    setFormData({
      ...formData,
      propertyDetails: {
        address: {
          street: address.address, // This now contains the complete address with house number
          city: address.city,
          state: address.state,
          zipCode: address.zipCode,
          fullAddress: address.fullAddress,
        },
      },
    });

    // Move to property verification step
    goToStep(2);

    // Fetch property details in the background
    await fetchPropertyDetails(address);
  };

  const fetchPropertyDetails = async (address: AutoCompleteResult) => {
    try {
      setPropertyDetailsError(null);
      console.log('Fetching property details for:', address);

      const response = await fetch('/api/property/details', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          propertyId: address.id,
          address: {
            street: address.address,
            city: address.city,
            state: address.state,
            zipCode: address.zipCode,
          },
        }),
      });

      if (!response.ok) {
        throw new Error(`Property details API failed: ${response.status}`);
      }

      const result = await response.json();
      console.log('Property details result:', result);

      if (result.success && result.propertyDetails) {
        // Update form data with enhanced property details
        setFormData(prev => ({
          ...prev,
          propertyDetails: {
            ...prev.propertyDetails,
            ...result.propertyDetails,
          },
        }));
        setPropertyDetailsLoaded(true);

        // Get required forms based on enhanced property data
        const forms = getRequiredForms(
          result.propertyDetails.address.state,
          result.propertyDetails.propertyType || 'SINGLE_FAMILY',
          result.propertyDetails.yearBuilt || 1990
        );
        setRequiredForms(forms);
      } else {
        throw new Error('Property details not found');
      }
    } catch (error) {
      console.error('Error fetching property details:', error);
      setPropertyDetailsError(error instanceof Error ? error.message : 'Failed to fetch property details');

      // Still get basic required forms based on state
      if (formData.propertyDetails?.address) {
        const forms = getRequiredForms(
          formData.propertyDetails.address.state,
          'SINGLE_FAMILY',
          1990
        );
        setRequiredForms(forms);
      }
    } finally {
      setLoading(false);
    }
  };

  const handleGenerateAgreement = async () => {
    try {
      setIsGenerating(true);
      console.log('Generating agreement with data:', formData);

      // Validate required data
      if (!formData.propertyDetails?.address) {
        alert('Property address is required');
        return;
      }

      if (!formData.agreementType) {
        alert('Please select an agreement type');
        return;
      }

      if (!formData.buyerInfo?.name || !formData.buyerInfo?.email) {
        alert('Buyer name and email are required');
        return;
      }

      if (!formData.offerDetails?.offerPrice) {
        alert('Offer price is required');
        return;
      }

      // TEMPORARILY BYPASS PAYMENT PROCESSING FOR TESTING
      console.log('🚀 BYPASSING PAYMENT - GENERATING PDF DIRECTLY');

      // Determine agreement parameters based on type
      const includeSellerName = formData.agreementType === 'OFFICIAL';
      const includeForms = formData.agreementType === 'OFFICIAL';

      console.log('Agreement parameters:', {
        agreementType: formData.agreementType,
        includeSellerName,
        includeForms,
        formsCount: requiredForms.length
      });

      // Generate and download PDF directly (bypassing API for testing)
      try {
        const { savePurchaseAgreement } = await import('@/app/lib/pdf-generator');

        console.log('Calling PDF generator with form data:', {
          propertyAddress: formData.propertyDetails?.address?.fullAddress,
          buyerName: formData.buyerInfo?.name,
          offerPrice: formData.offerDetails?.offerPrice,
          agreementType: formData.agreementType
        });

        const filename = await savePurchaseAgreement(
          formData,
          includeSellerName,
          includeForms
        );

        console.log('✅ PDF generated successfully:', filename);

        // Enhanced success message with better formatting
        const agreementTypeText = formData.agreementType === 'OFFICIAL'
          ? 'Complete Agreement (with seller name & forms)'
          : 'Initial Agreement (basic)';

        const stateCode = formData.propertyDetails?.address?.state?.toUpperCase() || '';
        const stateName = stateCode ? ` for ${stateCode}` : '';

        alert(`🎉 Agreement Generated Successfully!\n\n📄 File: ${filename}\n📋 Type: ${agreementTypeText}\n🏠 Property${stateName}\n💰 Offer: ${formData.offerDetails?.offerPrice ? `$${formData.offerDetails.offerPrice.toLocaleString()}` : 'N/A'}\n\n💡 Payment processing bypassed for testing.\n\nThe PDF has been downloaded to your Downloads folder.`);

      } catch (pdfError) {
        console.error('❌ PDF generation failed:', pdfError);
        throw new Error(`PDF generation failed: ${pdfError instanceof Error ? pdfError.message : 'Unknown PDF error'}`);
      }

    } catch (error) {
      console.error('Error generating agreement:', error);
      alert(`Error generating agreement: ${error instanceof Error ? error.message : 'Unknown error'}`);
    } finally {
      setIsGenerating(false);
    }
  };

  const handleMockDataFill = (mockData: any) => {
    setFormData({ ...formData, ...mockData });
    // If we have property details, get required forms
    if (mockData.propertyDetails?.address) {
      const forms = getRequiredForms(
        mockData.propertyDetails.address.state,
        mockData.propertyDetails.propertyType || 'SINGLE_FAMILY',
        mockData.propertyDetails.yearBuilt || 1990
      );
      setRequiredForms(forms);
    }
  };

  const renderStep = () => {
    switch (formData.step) {
      case 1:
        return (
          <div className="space-y-6">
            <div>
              <h2 className="text-2xl font-bold text-gray-900">Enter Property Address</h2>
              <p className="mt-2 text-gray-600">
                Start by entering the property address or pasting a listing URL
              </p>
            </div>
            <AddressAutocomplete onSelect={handleAddressSelect} />
          </div>
        );

      case 2:
        return (
          <div className="space-y-6">
            <div>
              <h2 className="text-2xl font-bold text-gray-900">Property Verification</h2>
              <p className="mt-2 text-gray-600">
                We're gathering property details and verifying the information. This is not another address input - we're just confirming the property you selected.
              </p>
            </div>

            {formData.propertyDetails?.address && (
              <div className="bg-green-50 border border-green-200 rounded-lg p-6">
                <div className="flex items-start">
                  <CheckCircle2 className="w-5 h-5 text-green-600 mt-0.5" />
                  <div className="ml-3 flex-1">
                    <h3 className="font-medium text-green-900 mb-2">✅ Selected Property Address</h3>
                    <p className="font-medium text-gray-900">
                      {formData.propertyDetails.address.street}
                    </p>
                    <p className="text-gray-600">
                      {formData.propertyDetails.address.city}, {formData.propertyDetails.address.state}{' '}
                      {formData.propertyDetails.address.zipCode}
                    </p>
                    <p className="text-sm text-green-700 mt-2">
                      ✓ Address confirmed - fetching additional property details...
                    </p>
                  </div>
                </div>
              </div>
            )}

            {loading && (
              <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
                <div className="flex items-center">
                  <Loader2 className="w-5 h-5 text-blue-600 animate-spin" />
                  <div className="ml-3">
                    <p className="text-sm text-blue-800">
                      Fetching property details from Real Estate API...
                    </p>
                  </div>
                </div>
              </div>
            )}

            {propertyDetailsLoaded && formData.propertyDetails && (
              <div className="bg-green-50 border border-green-200 rounded-lg p-4">
                <div className="flex items-start">
                  <CheckCircle2 className="w-5 h-5 text-green-600 mt-0.5" />
                  <div className="ml-3 flex-1">
                    <h3 className="font-medium text-green-900">Property Details Found</h3>
                    <div className="mt-2 space-y-1 text-sm text-green-800">
                      {formData.propertyDetails.propertyType && (
                        <p>Type: {formData.propertyDetails.propertyType}</p>
                      )}
                      {formData.propertyDetails.yearBuilt && (
                        <p>Year Built: {formData.propertyDetails.yearBuilt}</p>
                      )}
                      {formData.propertyDetails.bedrooms && formData.propertyDetails.bathrooms && (
                        <p>Bedrooms: {formData.propertyDetails.bedrooms}, Bathrooms: {formData.propertyDetails.bathrooms}</p>
                      )}
                      {formData.propertyDetails.squareFeet && (
                        <p>Square Feet: {formData.propertyDetails.squareFeet.toLocaleString()}</p>
                      )}
                      {formData.propertyDetails.legalDescription && (
                        <p>Legal Description: {formData.propertyDetails.legalDescription}</p>
                      )}
                    </div>
                  </div>
                </div>
              </div>
            )}

            {propertyDetailsError && (
              <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-4">
                <div className="flex items-start">
                  <AlertCircle className="w-5 h-5 text-yellow-600 mt-0.5" />
                  <div className="ml-3">
                    <p className="text-sm text-yellow-800">
                      {propertyDetailsError}. We'll continue with basic property information.
                    </p>
                  </div>
                </div>
              </div>
            )}

            <div className="flex gap-4">
              <button
                onClick={() => goToStep(1)}
                className="flex-1 px-4 py-3 border border-gray-300 rounded-lg hover:bg-gray-50"
              >
                <ArrowLeft className="w-4 h-4 inline mr-2" />
                Go Back
              </button>
              <button
                onClick={() => goToStep(3)}
                disabled={loading}
                className="flex-1 px-4 py-3 bg-primary-600 text-white rounded-lg hover:bg-primary-700 disabled:opacity-50"
              >
                {loading ? (
                  <Loader2 className="w-4 h-4 animate-spin mx-auto" />
                ) : (
                  <>
                    Continue to Forms
                    <ArrowRight className="w-4 h-4 inline ml-2" />
                  </>
                )}
              </button>
            </div>
          </div>
        );

      case 3:
        return (
          <div className="space-y-6">
            <div>
              <h2 className="text-2xl font-bold text-gray-900">Required Forms</h2>
              <p className="mt-2 text-gray-600">
                Based on the property location, these forms are required for your offer
              </p>
            </div>

            <div className="space-y-4">
              {requiredForms.map((form) => (
                <div
                  key={form.id}
                  className="bg-white border border-gray-200 rounded-lg p-4"
                >
                  <div className="flex items-start">
                    <CheckCircle2 className="w-5 h-5 text-green-500 mt-0.5" />
                    <div className="ml-3 flex-1">
                      <h3 className="font-medium text-gray-900">{form.name}</h3>
                      <p className="text-sm text-gray-600 mt-1">{form.description}</p>
                    </div>
                    {form.required && (
                      <span className="text-xs bg-red-100 text-red-800 px-2 py-1 rounded">
                        Required
                      </span>
                    )}
                  </div>
                </div>
              ))}
            </div>

            <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
              <h3 className="font-medium text-blue-900">Agreement Options</h3>
              <div className="mt-3 space-y-2">
                <label className="flex items-start cursor-pointer">
                  <input
                    type="radio"
                    name="agreementType"
                    value="PARTIAL_FREE"
                    onChange={(e) =>
                      setFormData({ ...formData, agreementType: e.target.value as any })
                    }
                    className="mt-1"
                  />
                  <div className="ml-3">
                    <p className="font-medium">Initial Agreement (Free)</p>
                    <p className="text-sm text-gray-600">
                      Purchase agreement only, no seller name
                    </p>
                  </div>
                </label>
                <label className="flex items-start cursor-pointer">
                  <input
                    type="radio"
                    name="agreementType"
                    value="OFFICIAL"
                    onChange={(e) =>
                      setFormData({ ...formData, agreementType: e.target.value as any })
                    }
                    className="mt-1"
                  />
                  <div className="ml-3">
                    <p className="font-medium">Complete Agreement ($5)</p>
                    <p className="text-sm text-gray-600">
                      All forms included with seller name
                    </p>
                  </div>
                </label>
              </div>
            </div>

            <div className="flex gap-4">
              <button
                onClick={() => goToStep(2)}
                className="flex-1 px-4 py-3 border border-gray-300 rounded-lg hover:bg-gray-50"
              >
                <ArrowLeft className="w-4 h-4 inline mr-2" />
                Go Back
              </button>
              <button
                onClick={() => goToStep(4)}
                disabled={!formData.agreementType}
                className="flex-1 px-4 py-3 bg-primary-600 text-white rounded-lg hover:bg-primary-700 disabled:opacity-50"
              >
                Continue
                <ArrowRight className="w-4 h-4 inline ml-2" />
              </button>
            </div>
          </div>
        );

      case 4:
        return (
          <div className="space-y-6">
            <div>
              <h2 className="text-2xl font-bold text-gray-900">Offer Details</h2>
              <p className="mt-2 text-gray-600">
                Enter your offer information
              </p>
            </div>

            <form className="space-y-6">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Buyer Name
                  </label>
                  <input
                    type="text"
                    value={formData.buyerInfo?.name || ''}
                    onChange={(e) => setFormData({
                      ...formData,
                      buyerInfo: { ...formData.buyerInfo, name: e.target.value, email: formData.buyerInfo?.email || '' }
                    })}
                    className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500"
                    placeholder="John Doe"
                    required
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Offer Price
                  </label>
                  <div className="relative">
                    <span className="absolute left-3 top-2 text-gray-500">$</span>
                    <input
                      type="text"
                      value={formData.offerDetails?.offerPrice ? formatCurrencyInput(formData.offerDetails.offerPrice.toString()) : ''}
                      onChange={(e) => {
                        const numericValue = parseCurrencyInput(e.target.value);
                        setFormData({
                          ...formData,
                          offerDetails: {
                            ...formData.offerDetails,
                            offerPrice: numericValue,
                            earnestDeposit: formData.offerDetails?.earnestDeposit || 0,
                            earnestDepositDays: formData.offerDetails?.earnestDepositDays || 3,
                            inspectionDays: formData.offerDetails?.inspectionDays || 7,
                            offerExpirationDate: formData.offerDetails?.offerExpirationDate || '',
                            paymentType: formData.offerDetails?.paymentType || 'CASH'
                          }
                        });
                      }}
                      onKeyDown={(e) => {
                        // Allow: backspace, delete, tab, escape, enter, decimal point
                        if ([46, 8, 9, 27, 13, 110, 190].indexOf(e.keyCode) !== -1 ||
                            // Allow: Ctrl+A, Ctrl+C, Ctrl+V, Ctrl+X
                            (e.keyCode === 65 && e.ctrlKey === true) ||
                            (e.keyCode === 67 && e.ctrlKey === true) ||
                            (e.keyCode === 86 && e.ctrlKey === true) ||
                            (e.keyCode === 88 && e.ctrlKey === true) ||
                            // Allow: home, end, left, right
                            (e.keyCode >= 35 && e.keyCode <= 39)) {
                          return;
                        }
                        // Ensure that it is a number and stop the keypress
                        if ((e.shiftKey || (e.keyCode < 48 || e.keyCode > 57)) && (e.keyCode < 96 || e.keyCode > 105)) {
                          e.preventDefault();
                        }
                      }}
                      className="w-full pl-8 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500"
                      placeholder="450,000.00"
                      required
                    />
                  </div>
                  <p className="text-xs text-gray-500 mt-1">Enter numbers only - formatting will be applied automatically</p>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Title Company
                  </label>
                  <input
                    type="text"
                    value={formData.offerDetails?.titleCompany || ''}
                    onChange={(e) => setFormData({
                      ...formData,
                      offerDetails: { ...formData.offerDetails, titleCompany: e.target.value }
                    })}
                    className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500"
                    placeholder="Optional"
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Earnest Deposit
                  </label>
                  <div className="relative">
                    <span className="absolute left-3 top-2 text-gray-500">$</span>
                    <input
                      type="text"
                      value={formData.offerDetails?.earnestDeposit ? formatCurrencyInput(formData.offerDetails.earnestDeposit.toString()) : ''}
                      onChange={(e) => {
                        const numericValue = parseCurrencyInput(e.target.value);
                        setFormData({
                          ...formData,
                          offerDetails: { ...formData.offerDetails, earnestDeposit: numericValue }
                        });
                      }}
                      onKeyDown={(e) => {
                        // Allow: backspace, delete, tab, escape, enter, decimal point
                        if ([46, 8, 9, 27, 13, 110, 190].indexOf(e.keyCode) !== -1 ||
                            // Allow: Ctrl+A, Ctrl+C, Ctrl+V, Ctrl+X
                            (e.keyCode === 65 && e.ctrlKey === true) ||
                            (e.keyCode === 67 && e.ctrlKey === true) ||
                            (e.keyCode === 86 && e.ctrlKey === true) ||
                            (e.keyCode === 88 && e.ctrlKey === true) ||
                            // Allow: home, end, left, right
                            (e.keyCode >= 35 && e.keyCode <= 39)) {
                          return;
                        }
                        // Ensure that it is a number and stop the keypress
                        if ((e.shiftKey || (e.keyCode < 48 || e.keyCode > 57)) && (e.keyCode < 96 || e.keyCode > 105)) {
                          e.preventDefault();
                        }
                      }}
                      className="w-full pl-8 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500"
                      placeholder="5,000.00"
                      required
                    />
                  </div>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Earnest Deposit Days (1-5)
                  </label>
                  <input
                    type="number"
                    value={formData.offerDetails?.earnestDepositDays || 3}
                    onChange={(e) => setFormData({
                      ...formData,
                      offerDetails: { ...formData.offerDetails, earnestDepositDays: parseInt(e.target.value) || 3 }
                    })}
                    min="1"
                    max="5"
                    className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500"
                    placeholder="3"
                    required
                  />
                  <p className="text-xs text-gray-500 mt-1">Days to deliver earnest money (1-5 days)</p>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Inspection Period (3-30 days)
                  </label>
                  <input
                    type="number"
                    value={formData.offerDetails?.inspectionDays || 7}
                    onChange={(e) => {
                      const days = parseInt(e.target.value) || 7;
                      if (days < 3 || days > 30) return; // Enforce validation
                      setFormData({
                        ...formData,
                        offerDetails: { ...formData.offerDetails, inspectionDays: days }
                      });
                    }}
                    min="3"
                    max="30"
                    className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500"
                    placeholder="7"
                    required
                  />
                  <p className="text-xs text-gray-500 mt-1">Recommended: 7-15 days. Minimum: 3 days, Maximum: 30 days</p>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Offer Expiration Date
                  </label>
                  <input
                    type="date"
                    value={formData.offerDetails?.offerExpirationDate || ''}
                    onChange={(e) => setFormData({
                      ...formData,
                      offerDetails: { ...formData.offerDetails, offerExpirationDate: e.target.value }
                    })}
                    min={new Date().toISOString().split('T')[0]} // Today's date minimum
                    className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500"
                    required
                  />
                  <p className="text-xs text-gray-500 mt-1">Date only (time not required)</p>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Payment Type
                  </label>
                  <select
                    value={formData.offerDetails?.paymentType || 'CASH'}
                    onChange={(e) => setFormData({
                      ...formData,
                      offerDetails: { ...formData.offerDetails, paymentType: e.target.value as any }
                    })}
                    className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500"
                    required
                  >
                    <option value="CASH">Cash</option>
                    <option value="CONVENTIONAL">Conventional Loan</option>
                    <option value="FHA">FHA Loan</option>
                    <option value="VA">VA Loan</option>
                    <option value="DSCR">DSCR Loan</option>
                  </select>
                </div>
              </div>

              <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-4">
                <p className="text-sm text-yellow-800">
                  To save your progress, please enter your email address:
                </p>
                <input
                  type="email"
                  value={formData.buyerInfo?.email || ''}
                  onChange={(e) => setFormData({
                    ...formData,
                    buyerInfo: { ...formData.buyerInfo, email: e.target.value, name: formData.buyerInfo?.name || '' }
                  })}
                  className="mt-2 w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500"
                  placeholder="<EMAIL>"
                  required
                />
              </div>

              <div className="flex gap-4">
                <button
                  type="button"
                  onClick={() => goToStep(3)}
                  className="flex-1 px-4 py-3 border border-gray-300 rounded-lg hover:bg-gray-50"
                >
                  <ArrowLeft className="w-4 h-4 inline mr-2" />
                  Go Back
                </button>
                <button
                  type="button"
                  onClick={() => goToStep(5)}
                  className="flex-1 px-4 py-3 bg-primary-600 text-white rounded-lg hover:bg-primary-700"
                >
                  Save & Continue
                  <ArrowRight className="w-4 h-4 inline ml-2" />
                </button>
              </div>
            </form>
          </div>
        );

      case 5:
        return (
          <div className="space-y-6">
            <div>
              <h2 className="text-2xl font-bold text-gray-900">Generate Agreement</h2>
              <p className="mt-2 text-gray-600">
                Review and generate your purchase agreement
              </p>
            </div>

            <div className="bg-gray-50 p-6 rounded-lg space-y-4">
              <h3 className="font-medium text-gray-900">Agreement Summary</h3>
              {formData.propertyDetails && (
                <div>
                  <p className="text-sm text-gray-600">Property:</p>
                  <p className="font-medium">{formData.propertyDetails.address.fullAddress}</p>
                </div>
              )}
              {formData.agreementType && (
                <div>
                  <p className="text-sm text-gray-600">Agreement Type:</p>
                  <p className="font-medium">
                    {formData.agreementType === 'PARTIAL_FREE'
                      ? 'Initial Agreement (Free)'
                      : 'Complete Agreement ($5)'}
                  </p>
                </div>
              )}
            </div>

            <div className="flex gap-4">
              <button
                onClick={() => goToStep(4)}
                className="flex-1 px-4 py-3 border border-gray-300 rounded-lg hover:bg-gray-50"
              >
                <ArrowLeft className="w-4 h-4 inline mr-2" />
                Go Back
              </button>
              <button
                onClick={handleGenerateAgreement}
                disabled={isGenerating}
                className="flex-1 px-4 py-3 bg-green-600 text-white rounded-lg hover:bg-green-700 disabled:opacity-50 disabled:cursor-not-allowed"
              >
                {isGenerating ? (
                  <>
                    <Loader2 className="w-4 h-4 animate-spin inline mr-2" />
                    Generating...
                  </>
                ) : (
                  'Generate Agreement'
                )}
              </button>
            </div>
          </div>
        );

      default:
        return null;
    }
  };

  return (
    <div className="min-h-screen bg-gray-50">
      {formData.step > 1 && (
        <ProgressTracker
          currentStep={formData.step}
          propertyAddress={formData.propertyDetails?.address.fullAddress}
        />
      )}

      <div className="max-w-3xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
        <AnimatePresence mode="wait" custom={direction}>
          <motion.div
            key={formData.step}
            custom={direction}
            variants={slideVariants}
            initial="enter"
            animate="center"
            exit="exit"
            transition={{
              x: { type: 'spring', stiffness: 300, damping: 30 },
              opacity: { duration: 0.2 },
            }}
            className="bg-white rounded-lg shadow-lg p-8"
          >
            {renderStep()}
          </motion.div>
        </AnimatePresence>
      </div>

      {/* Developer Tools */}
      <DeveloperTools
        onMockDataFill={handleMockDataFill}
        currentFormData={formData}
      />
    </div>
  );
}