'use client';

import { Check } from 'lucide-react';

interface Step {
  id: number;
  name: string;
  description?: string;
}

const steps: Step[] = [
  { id: 1, name: 'Property Address', description: 'Enter property details' },
  { id: 2, name: 'Verify Address', description: 'Confirm address' },
  { id: 3, name: 'Required Forms', description: 'Review forms' },
  { id: 4, name: 'Offer Details', description: 'Enter offer information' },
  { id: 5, name: 'Generate Agreement', description: 'Create document' },
];

interface ProgressTrackerProps {
  currentStep: number;
  propertyAddress?: string;
}

export default function ProgressTracker({ currentStep, propertyAddress }: ProgressTrackerProps) {
  return (
    <div className="w-full py-4 px-4 sm:px-6 lg:px-8 bg-white shadow-sm">
      <div className="max-w-7xl mx-auto">
        {propertyAddress && (
          <p className="text-sm text-gray-600 mb-4">Property: {propertyAddress}</p>
        )}
        <nav aria-label="Progress">
          <ol role="list" className="flex items-center justify-between">
            {steps.map((step, stepIdx) => (
              <li key={step.name} className={stepIdx !== steps.length - 1 ? 'flex-1' : ''}>
                <div className="flex items-center">
                  <div
                    className={`relative flex items-center justify-center w-10 h-10 rounded-full border-2 ${
                      step.id < currentStep
                        ? 'bg-primary-600 border-primary-600'
                        : step.id === currentStep
                        ? 'border-primary-600 bg-white'
                        : 'border-gray-300 bg-white'
                    }`}
                  >
                    {step.id < currentStep ? (
                      <Check className="w-6 h-6 text-white" />
                    ) : (
                      <span
                        className={`text-sm font-medium ${
                          step.id === currentStep ? 'text-primary-600' : 'text-gray-500'
                        }`}
                      >
                        {step.id}
                      </span>
                    )}
                  </div>
                  {stepIdx !== steps.length - 1 && (
                    <div
                      className={`flex-1 h-0.5 ml-4 ${
                        step.id < currentStep ? 'bg-primary-600' : 'bg-gray-300'
                      }`}
                    />
                  )}
                </div>
                <div className="mt-2">
                  <p
                    className={`text-xs font-medium ${
                      step.id <= currentStep ? 'text-gray-900' : 'text-gray-500'
                    }`}
                  >
                    {step.name}
                  </p>
                  {step.description && (
                    <p className="text-xs text-gray-500">{step.description}</p>
                  )}
                </div>
              </li>
            ))}
          </ol>
        </nav>
      </div>
    </div>
  );
}