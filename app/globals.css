@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  * {
    @apply border-neutral-200;
  }
  body {
    @apply bg-white text-gray-900;
  }

  /* Prevent scrolling on homepage */
  .homepage-container {
    height: 100vh;
    overflow: hidden;
  }
}

@layer utilities {
  .step-transition {
    @apply transition-all duration-300 ease-in-out;
  }

  /* Custom animation delays */
  .animation-delay-2000 {
    animation-delay: 2s;
  }

  .animation-delay-4000 {
    animation-delay: 4s;
  }

  /* Gradient text animation */
  .gradient-text {
    background: linear-gradient(-45deg, #ee7752, #e73c7e, #23a6d5, #23d5ab);
    background-size: 400% 400%;
    animation: gradient 15s ease infinite;
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
  }

  @keyframes gradient {
    0% {
      background-position: 0% 50%;
    }
    50% {
      background-position: 100% 50%;
    }
    100% {
      background-position: 0% 50%;
    }
  }

  /* Backdrop blur fallback */
  .backdrop-blur-fallback {
    background-color: rgba(255, 255, 255, 0.1);
  }

  /* Smooth transitions for all interactive elements */
  .smooth-transition {
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  }

  /* Text truncation utilities */
  .line-clamp-3 {
    display: -webkit-box;
    -webkit-line-clamp: 3;
    -webkit-box-orient: vertical;
    overflow: hidden;
  }

  /* Responsive text utilities */
  .text-responsive {
    font-size: clamp(0.875rem, 2.5vw, 1.125rem);
  }

  /* Prevent text selection on animated elements */
  .no-select {
    -webkit-user-select: none;
    -moz-user-select: none;
    -ms-user-select: none;
    user-select: none;
  }

  /* Improved backdrop blur for better browser support */
  .backdrop-blur-enhanced {
    backdrop-filter: blur(12px);
    -webkit-backdrop-filter: blur(12px);
  }

  /* Safe area padding for mobile devices */
  .safe-area-padding {
    padding-left: env(safe-area-inset-left);
    padding-right: env(safe-area-inset-right);
    padding-bottom: env(safe-area-inset-bottom);
  }

  /* Responsive breakpoint utilities */
  @media (max-width: 320px) {
    .text-xs-mobile {
      font-size: 0.625rem;
    }
    .p-xs-mobile {
      padding: 0.5rem;
    }
  }

  /* Landscape mobile optimization */
  @media (max-height: 500px) and (orientation: landscape) {
    .landscape-compact {
      padding-top: 0.5rem;
      padding-bottom: 0.5rem;
    }
    .landscape-compact .space-y-6 > * + * {
      margin-top: 1rem;
    }
    .landscape-compact .space-y-4 > * + * {
      margin-top: 0.5rem;
    }
  }

  /* High DPI display optimizations */
  @media (-webkit-min-device-pixel-ratio: 2), (min-resolution: 192dpi) {
    .backdrop-blur-enhanced {
      backdrop-filter: blur(8px);
      -webkit-backdrop-filter: blur(8px);
    }
  }
}
