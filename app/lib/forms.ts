import { RequiredForm, PropertyType } from '@/app/types';

// Map of state codes to their full names for reference
const STATE_NAMES: Record<string, string> = {
  'AL': 'Alabama',
  'AK': 'Alaska',
  'AZ': 'Arizona',
  'AR': 'Arkansas',
  'CA': 'California',
  'CO': 'Colorado',
  'CT': 'Connecticut',
  'DE': 'Delaware',
  'FL': 'Florida',
  'GA': 'Georgia',
  'HI': 'Hawaii',
  'ID': 'Idaho',
  'IL': 'Illinois',
  'IN': 'Indiana',
  'IA': 'Iowa',
  'KS': 'Kansas',
  'KY': 'Kentucky',
  'LA': 'Louisiana',
  'ME': 'Maine',
  'MD': 'Maryland',
  'MA': 'Massachusetts',
  'MI': 'Michigan',
  'MN': 'Minnesota',
  'MS': 'Mississippi',
  'MO': 'Missouri',
  'MT': 'Montana',
  'NE': 'Nebraska',
  'NV': 'Nevada',
  'NH': 'New Hampshire',
  'NJ': 'New Jersey',
  'NM': 'New Mexico',
  'NY': 'New York',
  'NC': 'North Carolina',
  'ND': 'North Dakota',
  'OH': 'Ohio',
  'OK': 'Oklahoma',
  'OR': 'Oregon',
  'PA': 'Pennsylvania',
  'RI': 'Rhode Island',
  'SC': 'South Carolina',
  'SD': 'South Dakota',
  'TN': 'Tennessee',
  'TX': 'Texas',
  'UT': 'Utah',
  'VT': 'Vermont',
  'VA': 'Virginia',
  'WA': 'Washington',
  'WV': 'West Virginia',
  'WI': 'Wisconsin',
  'WY': 'Wyoming',
  'DC': 'District of Columbia'
};

// Developer testing mode flag
let DEV_MODE = process.env.NEXT_PUBLIC_DEV_MODE === 'true' || process.env.DEVELOPER_MODE === 'true';

export function setDevMode(enabled: boolean): void {
  DEV_MODE = enabled;
}

export function isDevMode(): boolean {
  return DEV_MODE || process.env.NODE_ENV === 'development';
}

export function isDeveloperModeEnabled(): boolean {
  return process.env.DEVELOPER_MODE === 'true';
}

// Mock data for developer testing
export function getMockFormData() {
  return {
    propertyDetails: {
      address: {
        street: '123 Test Street',
        city: 'Test City',
        state: 'CA',
        zipCode: '90210',
        fullAddress: '123 Test Street, Test City, CA 90210'
      },
      propertyType: 'SINGLE_FAMILY' as const,
      yearBuilt: 1985,
      squareFootage: 2500,
      lotSize: 0.25,
      bedrooms: 3,
      bathrooms: 2
    },
    buyerInfo: {
      name: 'John Doe',
      email: '<EMAIL>',
      phone: '(*************',
      address: {
        street: '456 Buyer Lane',
        city: 'Buyer City',
        state: 'CA',
        zipCode: '90211',
        fullAddress: '456 Buyer Lane, Buyer City, CA 90211'
      }
    },
    offerDetails: {
      offerPrice: 500000,
      titleCompany: 'Test Title Company',
      earnestDeposit: 10000,
      earnestDepositDays: 3,
      inspectionDays: 10,
      offerExpirationDate: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000).toISOString().split('T')[0],
      paymentType: 'CASH' as const,
      closingDate: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000).toISOString().split('T')[0],
      additionalTerms: 'Property sold as-is. No repairs requested.'
    }
  };
}

export function getRequiredForms(
  state: string, 
  propertyType: PropertyType = 'SINGLE_FAMILY', 
  yearBuilt?: number
): RequiredForm[] {
  // Normalize state code to uppercase
  const stateCode = state.toUpperCase();
  
  // Base forms that are always required
  const forms: RequiredForm[] = [
    {
      id: `${stateCode.toLowerCase()}-purchase-agreement`,
      name: `${STATE_NAMES[stateCode] || state} As-Is Purchase Agreement`,
      description: 'Main purchase agreement form',
      required: true,
    },
  ];

  // Lead-based paint disclosure (for homes built before 1978)
  if (yearBuilt && yearBuilt < 1978) {
    forms.push({
      id: 'lead-paint-disclosure',
      name: 'Lead-Based Paint Disclosure',
      description: 'Required for properties built before 1978',
      required: true,
    });
  }

  // State-specific forms
  switch (stateCode) {
    case 'AL':
      forms.push({
        id: 'al-property-disclosure',
        name: 'Alabama Property Disclosure Statement',
        description: 'Required disclosure for Alabama properties',
        required: true,
      });
      break;
      
    case 'AK':
      forms.push({
        id: 'ak-property-disclosure',
        name: 'Alaska Residential Real Property Transfer Disclosure Statement',
        description: 'Required disclosure for Alaska properties',
        required: true,
      });
      break;
      
    case 'AZ':
      forms.push({
        id: 'az-spds',
        name: 'Arizona Seller\'s Property Disclosure Statement (SPDS)',
        description: 'Required disclosure for Arizona properties',
        required: true,
      });
      forms.push({
        id: 'az-affidavit',
        name: 'Affidavit of Disclosure',
        description: 'Required affidavit for Arizona properties',
        required: true,
      });
      break;
      
    case 'AR':
      forms.push({
        id: 'ar-property-disclosure',
        name: 'Arkansas Property Condition Disclosure',
        description: 'Required disclosure for Arkansas properties',
        required: true,
      });
      break;
      
    case 'CA':
      forms.push({
        id: 'ca-tds',
        name: 'California Transfer Disclosure Statement (TDS)',
        description: 'Required disclosure for California properties',
        required: true,
      });
      forms.push({
        id: 'ca-natural-hazard',
        name: 'Natural Hazard Disclosure Statement',
        description: 'Required for all California properties',
        required: true,
      });
      forms.push({
        id: 'ca-earthquake',
        name: 'Earthquake Hazards Disclosure',
        description: 'Required for properties in earthquake zones',
        required: true,
      });
      forms.push({
        id: 'ca-environmental',
        name: 'Environmental Hazards Disclosure',
        description: 'Required environmental disclosure',
        required: true,
      });
      forms.push({
        id: 'ca-water-heater',
        name: 'Water Heater and Smoke Detector Statement',
        description: 'Required safety compliance statement',
        required: true,
      });
      forms.push({
        id: 'ca-buyer-seller-advisory',
        name: 'Statewide Buyer and Seller Advisory',
        description: 'Required advisory for all parties',
        required: true,
      });
      break;
      
    case 'CO':
      forms.push({
        id: 'co-property-disclosure',
        name: 'Colorado Seller\'s Property Disclosure',
        description: 'Required disclosure for Colorado properties',
        required: true,
      });
      forms.push({
        id: 'co-square-footage',
        name: 'Square Footage Disclosure',
        description: 'Required square footage disclosure',
        required: true,
      });
      break;
      
    case 'CT':
      forms.push({
        id: 'ct-property-report',
        name: 'Connecticut Residential Property Condition Report',
        description: 'Required disclosure for Connecticut properties',
        required: true,
      });
      break;
      
    case 'DE':
      forms.push({
        id: 'de-property-disclosure',
        name: 'Delaware Seller\'s Disclosure of Real Property Condition Report',
        description: 'Required disclosure for Delaware properties',
        required: true,
      });
      break;
      
    case 'FL':
      forms.push({
        id: 'fl-property-disclosure',
        name: 'Florida Property Disclosure Form',
        description: 'Property disclosure (optional but recommended)',
        required: false,
      });
      forms.push({
        id: 'fl-radon-gas',
        name: 'Florida Radon Gas Disclosure',
        description: 'Required for all Florida properties',
        required: true,
      });
      if (propertyType === 'CONDO' || propertyType === 'TOWNHOUSE') {
        forms.push({
          id: 'fl-hoa-disclosure',
          name: 'Florida Homeowners\' Association Disclosure',
          description: 'Required for properties with HOA',
          required: true,
        });
      }
      break;
      
    case 'GA':
      forms.push({
        id: 'ga-property-disclosure',
        name: 'Georgia Seller\'s Property Disclosure Statement',
        description: 'Required disclosure for Georgia properties',
        required: true,
      });
      break;
      
    case 'HI':
      forms.push({
        id: 'hi-property-disclosure',
        name: 'Hawaii Seller\'s Real Property Disclosure Statement',
        description: 'Required disclosure for Hawaii properties',
        required: true,
      });
      break;
      
    case 'ID':
      forms.push({
        id: 'id-property-disclosure',
        name: 'Idaho Property Condition Disclosure Act Form',
        description: 'Required disclosure for Idaho properties',
        required: true,
      });
      break;
      
    case 'IL':
      forms.push({
        id: 'il-property-disclosure',
        name: 'Illinois Residential Real Property Disclosure Report',
        description: 'Required disclosure for Illinois properties',
        required: true,
      });
      forms.push({
        id: 'il-radon',
        name: 'Illinois Radon Disclosure',
        description: 'Required radon disclosure',
        required: true,
      });
      break;
      
    case 'IN':
      forms.push({
        id: 'in-property-disclosure',
        name: 'Indiana Seller\'s Residential Real Estate Sales Disclosure',
        description: 'Required disclosure for Indiana properties',
        required: true,
      });
      break;
      
    case 'IA':
      forms.push({
        id: 'ia-property-disclosure',
        name: 'Iowa Residential Property Seller Disclosure Statement',
        description: 'Required disclosure for Iowa properties',
        required: true,
      });
      break;
      
    case 'KS':
      forms.push({
        id: 'ks-property-disclosure',
        name: 'Kansas Seller\'s Property Condition Disclosure Statement',
        description: 'Required disclosure for Kansas properties',
        required: true,
      });
      break;
      
    case 'KY':
      forms.push({
        id: 'ky-property-disclosure',
        name: 'Kentucky Seller\'s Disclosure of Property Conditions',
        description: 'Required disclosure for Kentucky properties',
        required: true,
      });
      break;
      
    case 'LA':
      forms.push({
        id: 'la-property-disclosure',
        name: 'Louisiana Property Disclosure Document',
        description: 'Required disclosure for Louisiana properties',
        required: true,
      });
      break;
      
    case 'ME':
      forms.push({
        id: 'me-property-disclosure',
        name: 'Maine Property Disclosure Statement',
        description: 'Required disclosure for Maine properties',
        required: true,
      });
      forms.push({
        id: 'me-water-waste',
        name: 'Private Water Supply and Waste Disposal Disclosure',
        description: 'Required for properties with private water/septic',
        required: false,
      });
      break;
      
    case 'MD':
      forms.push({
        id: 'md-property-disclosure',
        name: 'Maryland Residential Property Disclosure and Disclaimer Statement',
        description: 'Required disclosure for Maryland properties',
        required: true,
      });
      break;
      
    case 'MA':
      forms.push({
        id: 'ma-property-disclosure',
        name: 'Massachusetts Seller\'s Statement of Property Condition',
        description: 'Property disclosure (optional)',
        required: false,
      });
      forms.push({
        id: 'ma-title-v',
        name: 'Title V Septic System Disclosure',
        description: 'Required for properties with septic systems',
        required: false,
      });
      break;
      
    case 'MI':
      forms.push({
        id: 'mi-property-disclosure',
        name: 'Michigan Seller\'s Disclosure Statement',
        description: 'Required disclosure for Michigan properties',
        required: true,
      });
      break;
      
    case 'MN':
      forms.push({
        id: 'mn-property-disclosure',
        name: 'Minnesota Seller\'s Property Disclosure Statement',
        description: 'Required disclosure for Minnesota properties',
        required: true,
      });
      forms.push({
        id: 'mn-well-disclosure',
        name: 'Well Disclosure',
        description: 'Required for properties with wells',
        required: false,
      });
      forms.push({
        id: 'mn-septic-disclosure',
        name: 'Individual Sewage Treatment System Disclosure',
        description: 'Required for properties with septic systems',
        required: false,
      });
      break;
      
    case 'MS':
      forms.push({
        id: 'ms-property-disclosure',
        name: 'Mississippi Property Condition Disclosure Statement',
        description: 'Required disclosure for Mississippi properties',
        required: true,
      });
      break;
      
    case 'MO':
      forms.push({
        id: 'mo-property-disclosure',
        name: 'Missouri Seller\'s Disclosure Statement',
        description: 'Required disclosure for Missouri properties',
        required: true,
      });
      break;
      
    case 'MT':
      forms.push({
        id: 'mt-property-disclosure',
        name: 'Montana Property Disclosure Statement',
        description: 'Required disclosure for Montana properties',
        required: true,
      });
      break;
      
    case 'NE':
      forms.push({
        id: 'ne-property-disclosure',
        name: 'Nebraska Seller Property Condition Disclosure Statement',
        description: 'Required disclosure for Nebraska properties',
        required: true,
      });
      break;
      
    case 'NV':
      forms.push({
        id: 'nv-property-disclosure',
        name: 'Nevada Seller\'s Real Property Disclosure Form',
        description: 'Required disclosure for Nevada properties',
        required: true,
      });
      break;
      
    case 'NH':
      forms.push({
        id: 'nh-property-disclosure',
        name: 'New Hampshire Property Disclosure Statement',
        description: 'Required disclosure for New Hampshire properties',
        required: true,
      });
      break;
      
    case 'NJ':
      forms.push({
        id: 'nj-property-disclosure',
        name: 'New Jersey Seller\'s Property Condition Disclosure Statement',
        description: 'Required disclosure for New Jersey properties',
        required: true,
      });
      forms.push({
        id: 'nj-well-testing',
        name: 'Private Well Testing Act Notice',
        description: 'Required for properties with private wells',
        required: false,
      });
      break;
      
    case 'NM':
      forms.push({
        id: 'nm-property-disclosure',
        name: 'New Mexico Property Disclosure Statement',
        description: 'Required disclosure for New Mexico properties',
        required: true,
      });
      break;
      
    case 'NY':
      forms.push({
        id: 'ny-property-disclosure',
        name: 'New York Property Condition Disclosure Statement',
        description: 'Required disclosure for New York properties',
        required: true,
      });
      break;
      
    case 'NC':
      forms.push({
        id: 'nc-property-disclosure',
        name: 'North Carolina Residential Property and Owners\' Association Disclosure Statement',
        description: 'Required disclosure for North Carolina properties',
        required: true,
      });
      break;
      
    case 'ND':
      forms.push({
        id: 'nd-property-disclosure',
        name: 'North Dakota Property Condition Disclosure Statement',
        description: 'Required disclosure for North Dakota properties',
        required: true,
      });
      break;
      
    case 'OH':
      forms.push({
        id: 'oh-property-disclosure',
        name: 'Ohio Residential Property Disclosure Form',
        description: 'Required disclosure for Ohio properties',
        required: true,
      });
      break;
      
    case 'OK':
      forms.push({
        id: 'ok-property-disclosure',
        name: 'Oklahoma Residential Property Condition Disclosure Statement',
        description: 'Required disclosure for Oklahoma properties',
        required: true,
      });
      break;
      
    case 'OR':
      forms.push({
        id: 'or-property-disclosure',
        name: 'Oregon Seller\'s Property Disclosure Statement',
        description: 'Required disclosure for Oregon properties',
        required: true,
      });
      break;
      
    case 'PA':
      forms.push({
        id: 'pa-property-disclosure',
        name: 'Pennsylvania Seller Property Disclosure Statement',
        description: 'Required disclosure for Pennsylvania properties',
        required: true,
      });
      break;
      
    case 'RI':
      forms.push({
        id: 'ri-property-disclosure',
        name: 'Rhode Island Real Estate Disclosure Form',
        description: 'Required disclosure for Rhode Island properties',
        required: true,
      });
      break;
      
    case 'SC':
      forms.push({
        id: 'sc-property-disclosure',
        name: 'South Carolina Residential Property Condition Disclosure Statement',
        description: 'Required disclosure for South Carolina properties',
        required: true,
      });
      break;
      
    case 'SD':
      forms.push({
        id: 'sd-property-disclosure',
        name: 'South Dakota Seller\'s Property Condition Disclosure Statement',
        description: 'Required disclosure for South Dakota properties',
        required: true,
      });
      break;
      
    case 'TN':
      forms.push({
        id: 'tn-property-disclosure',
        name: 'Tennessee Residential Property Disclosure',
        description: 'Required disclosure for Tennessee properties',
        required: true,
      });
      break;
      
    case 'TX':
      forms.push({
        id: 'tx-property-disclosure',
        name: 'Texas Seller\'s Disclosure Notice',
        description: 'Required disclosure for Texas properties',
        required: true,
      });
      forms.push({
        id: 'tx-trec',
        name: 'TREC Information About Brokerage Services',
        description: 'Required for all Texas real estate transactions',
        required: true,
      });
      if (propertyType === 'CONDO' || propertyType === 'TOWNHOUSE') {
        forms.push({
          id: 'tx-hoa',
          name: 'MUD/PID/HOA Addendum',
          description: 'Required for properties with HOA/MUD/PID',
          required: true,
        });
      }
      break;
      
    case 'UT':
      forms.push({
        id: 'ut-property-disclosure',
        name: 'Utah Seller Property Condition Disclosure',
        description: 'Required disclosure for Utah properties',
        required: true,
      });
      break;
      
    case 'VT':
      forms.push({
        id: 'vt-property-disclosure',
        name: 'Vermont Property Disclosure Form',
        description: 'Required disclosure for Vermont properties',
        required: true,
      });
      break;
      
    case 'VA':
      forms.push({
        id: 'va-property-disclosure',
        name: 'Virginia Residential Property Disclosure Statement',
        description: 'Required disclosure for Virginia properties',
        required: true,
      });
      break;
      
    case 'WA':
      forms.push({
        id: 'wa-property-disclosure',
        name: 'Washington Seller Disclosure Statement',
        description: 'Required disclosure for Washington properties',
        required: true,
      });
      break;
      
    case 'DC':
      forms.push({
        id: 'dc-property-disclosure',
        name: 'DC Seller\'s Disclosure Statement',
        description: 'Required disclosure for DC properties',
        required: true,
      });
      break;
      
    case 'WV':
      forms.push({
        id: 'wv-property-disclosure',
        name: 'West Virginia Residential Property Disclosure Statement',
        description: 'Required disclosure for West Virginia properties',
        required: true,
      });
      break;
      
    case 'WI':
      forms.push({
        id: 'wi-property-disclosure',
        name: 'Wisconsin Real Estate Condition Report',
        description: 'Required disclosure for Wisconsin properties',
        required: true,
      });
      break;
      
    case 'WY':
      forms.push({
        id: 'wy-property-disclosure',
        name: 'Wyoming Seller Property Condition Disclosure',
        description: 'Required disclosure for Wyoming properties',
        required: true,
      });
      break;
      
    default:
      // For states without specific requirements, add a generic disclosure
      if (DEV_MODE) {
        // In dev mode, add all possible forms for testing
        forms.push({
          id: 'dev-all-state-disclosure',
          name: 'All State Disclosure (DEV MODE)',
          description: 'Development testing form',
          required: true,
        });
      } else {
        forms.push({
          id: 'generic-disclosure',
          name: 'Property Disclosure Statement',
          description: 'General property disclosure',
          required: true,
        });
      }
  }

  // Property type specific forms
  if (propertyType === 'CONDO' || propertyType === 'TOWNHOUSE') {
    forms.push({
      id: 'hoa-disclosure',
      name: 'HOA Disclosure and Addendum',
      description: 'Required for properties with HOA',
      required: true,
    });
    forms.push({
      id: 'condo-resale-cert',
      name: 'Condominium Resale Certificate',
      description: 'Required documentation from HOA',
      required: true,
    });
  }

  // Commercial property forms
  if (propertyType === 'COMMERCIAL') {
    forms.push({
      id: 'commercial-env-disclosure',
      name: 'Environmental Disclosures',
      description: 'Required environmental assessment',
      required: true,
    });
    forms.push({
      id: 'commercial-zoning',
      name: 'Zoning Compliance Certificate',
      description: 'Verification of zoning compliance',
      required: true,
    });
  }

  // Multi-family specific forms
  if (propertyType === 'MULTI_FAMILY') {
    forms.push({
      id: 'rent-roll',
      name: 'Rent Roll',
      description: 'Current tenant and rent information',
      required: true,
    });
    forms.push({
      id: 'tenant-estoppel',
      name: 'Tenant Estoppel Certificates',
      description: 'Tenant verification of lease terms',
      required: true,
    });
  }

  // As-Is Addendum (optional but recommended)
  forms.push({
    id: 'as-is-addendum',
    name: 'As-Is Addendum',
    description: 'Reinforces property is being sold as-is',
    required: false,
  });

  // Inspection waiver (optional)
  forms.push({
    id: 'inspection-waiver',
    name: 'Inspection Contingency Waiver',
    description: 'Optional waiver of inspection contingency',
    required: false,
  });

  return forms;
}

export function getFormsByAgreementType(
  agreementType: 'PARTIAL_FREE' | 'PARTIAL_PAID' | 'OFFICIAL',
  allForms: RequiredForm[]
): RequiredForm[] {
  switch (agreementType) {
    case 'PARTIAL_FREE':
      // Only include the main purchase agreement for free partial agreements
      return allForms.filter(form => form.id.includes('purchase-agreement'));
    case 'PARTIAL_PAID':
      // Include the main purchase agreement for paid partial agreements
      return allForms.filter(form => form.id.includes('purchase-agreement'));
    case 'OFFICIAL':
      // Include all required forms for official agreements
      return allForms.filter(form => form.required || DEV_MODE);
    default:
      return [];
  }
}

// Helper function to get state name from code
export function getStateName(stateCode: string): string {
  return STATE_NAMES[stateCode.toUpperCase()] || stateCode;
}

// Helper function to validate state code
export function isValidStateCode(stateCode: string): boolean {
  return stateCode.toUpperCase() in STATE_NAMES;
}

// Get all available states
export function getAllStates(): Array<{ code: string; name: string }> {
  return Object.entries(STATE_NAMES).map(([code, name]) => ({ code, name }));
}
