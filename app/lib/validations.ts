import { z } from 'zod';

export const addressSchema = z.object({
  street: z.string().min(1, 'Street address is required'),
  city: z.string().min(1, 'City is required'),
  state: z.string().length(2, 'State must be 2 characters'),
  zipCode: z.string().regex(/^\d{5}(-\d{4})?$/, 'Invalid ZIP code'),
  fullAddress: z.string(),
});

export const propertyDetailsSchema = z.object({
  address: addressSchema,
  listingPrice: z.number().optional(),
  legalDescription: z.string().optional(),
  propertyType: z.string().optional(),
  yearBuilt: z.number().optional(),
  bedrooms: z.number().optional(),
  bathrooms: z.number().optional(),
  squareFeet: z.number().optional(),
  lotSize: z.number().optional(),
  mls: z.string().optional(),
});

export const buyerInfoSchema = z.object({
  name: z.string().min(1, 'Name is required'),
  email: z.string().email('Invalid email address'),
  phone: z.string().optional(),
  address: addressSchema.optional(),
});

export const offerDetailsSchema = z.object({
  offerPrice: z.number().min(1, 'Offer price is required'),
  titleCompany: z.string().optional(),
  earnestDeposit: z.number().min(0, 'Earnest deposit must be positive'),
  earnestDepositDays: z.number().min(1).max(5, 'Must be between 1-5 days'),
  inspectionDays: z.number().min(3).max(30, 'Must be between 3-30 days'),
  offerExpirationDate: z.string(),
  paymentType: z.enum(['CASH', 'CONVENTIONAL', 'FHA', 'VA', 'DSCR']),
  closingDate: z.string().optional(),
  additionalTerms: z.string().optional(),
});

export const loginSchema = z.object({
  email: z.string().email('Invalid email address'),
  password: z.string().min(6, 'Password must be at least 6 characters'),
});

export const registerSchema = z.object({
  name: z.string().min(1, 'Name is required'),
  email: z.string().email('Invalid email address'),
  password: z.string().min(6, 'Password must be at least 6 characters'),
  confirmPassword: z.string(),
}).refine((data) => data.password === data.confirmPassword, {
  message: "Passwords don't match",
  path: ["confirmPassword"],
});