'use client';

import { useState } from 'react';
import { AnimatePresence, motion } from 'framer-motion';
import HeroSection from './components/HeroSection';
import MultiStepForm from './components/MultiStepForm';
import DeveloperTools from './components/DeveloperTools';
import { AutoCompleteResult } from './types';

export default function Home() {
  const [showForm, setShowForm] = useState(false);
  const [initialAddress, setInitialAddress] = useState<AutoCompleteResult | null>(null);
  const [formData, setFormData] = useState<any>(null);

  const handleAddressSelect = (address: AutoCompleteResult) => {
    setInitialAddress(address);
    setShowForm(true);
  };

  const handleMockDataFill = (data: any) => {
    setFormData(data);
    if (data.propertyDetails?.address) {
      const address: AutoCompleteResult = {
        id: 'mock-id',
        address: data.propertyDetails.address.street,
        city: data.propertyDetails.address.city,
        state: data.propertyDetails.address.state,
        zipCode: data.propertyDetails.address.zipCode,
        fullAddress: data.propertyDetails.address.fullAddress
      };
      setInitialAddress(address);
      setShowForm(true);
    }
  };

  return (
    <div className={!showForm ? 'homepage-container' : ''}>
      <AnimatePresence mode="wait">
        {!showForm ? (
          <motion.div
            key="hero"
            initial={{ opacity: 1 }}
            exit={{ opacity: 0, x: -100 }}
            transition={{ duration: 0.3 }}
            className="h-full"
          >
            <HeroSection onAddressSelect={handleAddressSelect} />
          </motion.div>
        ) : (
          <motion.div
            key="form"
            initial={{ opacity: 0, x: 100 }}
            animate={{ opacity: 1, x: 0 }}
            transition={{ duration: 0.3 }}
          >
            <MultiStepForm initialData={formData} initialAddress={initialAddress} />
          </motion.div>
        )}
      </AnimatePresence>

      {/* Developer Tools */}
      <DeveloperTools onMockDataFill={handleMockDataFill} currentFormData={formData} />
    </div>
  );
}
