Real Estate Purchase Agreement Generator
This is a Next.js application that allows users (wholesalers, Section 8 investors, and buyers) to create legally compliant as-is purchase agreements without requiring a real estate agent.

Key features:

Multi-step form for collecting property details, buyer information, and offer details
Address autocomplete integration with a real estate API
PDF generation for completed agreements
Stripe payment integration for premium features
User authentication and account management
Agreement storage and management in a PostgreSQL database via Prisma

The app uses modern technologies including:
Next.js 14.1.0 with App Router
React 18 with React Hook Form for form handling
Tailwind CSS for styling
Framer Motion for animations
Prisma ORM with PostgreSQL
Stripe for payments
Zod for form validation
The project appears to be a MVP (Minimum Viable Product) focused on streamlining the real estate agreement process for investors and wholesalers.

--


Look at my new project: /Users/<USER>/Desktop/REAPI-REPORT/re-dummy-mvp/s8algorithm/realestate-agreement

claude was working on it, but never finished. this website will use real estate forms from every state to put together purchase agreements that are sent to seller / listing agent. forms vary by state, so we need all 50. The one important factor is that the application uses the as-is purchase agreement from the state it is assigned to, meaning every state has their own. Following this we should also include other forms every state uses, example lead-based paint if 1978 or older. Given this is a saas production, please do allow for a developer bypass setting for testing all features. Pick up where claude left off building, so index entire codebase and map out a plan, tell me the plan and then execute. 


original idea:
1. Application Purpose:
-Wholesalers, Section 8 Investors, and buyers who do not have a real estate agent do not have the ability to officially  put together a solid as-is purchase agreement. 
-This is because they do not have access to the forms, they do not know which forms to use, and do not know how to correctly fill out the forms. 
-This can lead to issues where they might not be taken seriously or have their interest ignored.
-The website will do the following:
1. Upon loading site, it quickly discusses this in the hero section and then shows an input field for typing in the address or pasting the url of the listing (zillow for example). This then becomes a multi-step form. When pressing enter or pressing next, the landing/hero hides and animates (transition), to the next question. 

We will include an autocomplete address using: AutoComplete API
Type: POST  - Endpoint: https://api.realestateapi.com/v2/AutoComplete
The AutoComplete approximates like property searches based on Incomplete Address Parts and Combinations. Our AutoComplete algorithms are powered by machine learning and give you rich property lists without having to design tons of different and possibly complex Property Search queries.

Body Parameters:
search (string 3 minimum characters) - required
search_types (use A only - full address)

Crawl and read this API doc: https://developer.realestateapi.com/reference/autocomplete-api



Now appears a multi-form progress tracker at top with the step they are on with a address preview in small font.  
3. step 2, confirm the address is correct.  We can verify the address by using this api: Endpoint: https://api.realestateapi.com/v2/AddressVerification

Address Verification API - Type: POST

Crawl and read this API doc: https://developer.realestateapi.com/reference/address-verification-api

If address is not verifying, allow user override to skip to step 3. 

Step 3: Determine which forms are required for offer aubmission. This may include a lead-based paint disclosure, or rider, etc. Show the user which forms for this address are neccessary. Part of this is first knowing the state, after the system knows the state, type of home, etc, it will then tell the user which forms must also be filled out in offer submission. We also want to show the listing address, listing price, legal description, owner (dont show, just put owner name redacted until agreement generated),).

Step 4: Ask user if they want to generate an initial agreement (only includes purchase agreement, basically a way to propose interest through showing a partially completed agreement). This option will include an additional page inside the agrement at end with info saying if inital offer accepted, you will send over remaining forms (list remaining forms). 
second option: complete purchase agreement, includes all forms neccary. 

following this, we will now ask for all info we need to create and generate the purchase agreement.
-Buyer Name
-Offer Price
-Title Company (leave blank if none, add checkbox saying later / N/A)
-Earnest Deposit 
-Days for Earnest Deposit (up to 5)
-Inspection days: up to 14
-Offer expiration time
-Payment Type: Cash, Conventional, FHA, VA, DSCR
and anything else. 
Now show Save & Continue button. For Save & Continue, user must type their email so we can assign this draft to them so its not lost.
 
Step 4: 
Ask if they want a partial agreement without seller name, it is free. 
If not prompt for partial agreement with seller name ($2).
If not prompt for official agreement with all forms and seller name ($5)

Subscriptions: 
-25 Official Agreements ($15/month)  & 25 Unofficial Agreements
-50 Agreements ($25/month) & 50 Unofficial Agreements
-100 Agreement ($40/month) & 100 Unofficial Agreements

Guest:
Partial Agreement Free:
Step 5. Generate a partial as-is agreement. Do not include a seller name. Do not include any other forms. 

Partial Agreement Subscription: (guest payment)
Step 5: Generate a partial as-is agreement. Include seller name.

Official Agreement Paid or Subscriber: (guest payment)
Step 5: Generate a full as-is agreement. Include seller name. Include all other forms. 
~~
If logged in with active subscription: show remaining subscriptions allowed. If reamining above 0, allow for 
Step 5: Generate a partial as-is agreement. Include seller name.
If logged in with active subscription: show remaining subscriptions allowed. If reamining above 0, allow for 
Official Agreement Paid or Subscriber:
Step 5: Generate a full as-is agreement. Include seller name. Include all other forms.
~~

Real Estate Agreement Generator Project Analysis

Current State of the Project
Verify we have:

A multi-step form interface with progress tracking
Address autocomplete and verification using a real estate API
Form validation with Zod
State-specific form determination logic
PDF generation capabilities
Subscription plan structure in the database schema
Payment integration with Stripe

Plan to Complete the Project
1. State-Specific Agreement Templates
Create a library of state-specific as-is purchase agreement templates
Implement a system to select the correct template based on property state
2. Expand Form Requirements Logic
Enhance the getRequiredForms function to include all state-specific requirements
Add more property type conditions (condo, multi-family, etc.)
Complete the year-built logic for lead-based paint disclosure
3. PDF Generation Enhancement
Implement state-specific PDF generation
Add support for multiple forms in a single PDF or as separate files
Include proper formatting and styling for official documents
4. Developer Testing Mode
Add a developer bypass setting for testing all features
Create a mock API response system for development
5. Complete the Payment Flow
Finalize the subscription management system
Implement usage tracking for agreement generation limits
6. User Account Management
Complete the draft saving functionality
Add history of generated agreements


``
import { RequiredForm, PropertyType } from '@/app/types';

// Map of state codes to their full names for reference
const STATE_NAMES = {
  'AL': 'Alabama',
  'AK': 'Alaska',
  'AZ': 'Arizona',
  'AR': 'Arkansas',
  'CA': 'California',
  'CO': 'Colorado',
  'CT': 'Connecticut',
  'DE': 'Delaware',
  'FL': 'Florida',
  'GA': 'Georgia',
  'HI': 'Hawaii',
  'ID': 'Idaho',
  'IL': 'Illinois',
  'IN': 'Indiana',
  'IA': 'Iowa',
  'KS': 'Kansas',
  'KY': 'Kentucky',
  'LA': 'Louisiana',
  'ME': 'Maine',
  'MD': 'Maryland',
  'MA': 'Massachusetts',
  'MI': 'Michigan',
  'MN': 'Minnesota',
  'MS': 'Mississippi',
  'MO': 'Missouri',
  'MT': 'Montana',
  'NE': 'Nebraska',
  'NV': 'Nevada',
  'NH': 'New Hampshire',
  'NJ': 'New Jersey',
  'NM': 'New Mexico',
  'NY': 'New York',
  'NC': 'North Carolina',
  'ND': 'North Dakota',
  'OH': 'Ohio',
  'OK': 'Oklahoma',
  'OR': 'Oregon',
  'PA': 'Pennsylvania',
  'RI': 'Rhode Island',
  'SC': 'South Carolina',
  'SD': 'South Dakota',
  'TN': 'Tennessee',
  'TX': 'Texas',
  'UT': 'Utah',
  'VT': 'Vermont',
  'VA': 'Virginia',
  'WA': 'Washington',
  'WV': 'West Virginia',
  'WI': 'Wisconsin',
  'WY': 'Wyoming',
  'DC': 'District of Columbia'
};

// Developer testing mode flag
let DEV_MODE = false;

export function setDevMode(enabled: boolean): void {
  DEV_MODE = enabled;
}

export function isDevMode(): boolean {
  return DEV_MODE;
}

export function getRequiredForms(
  state: string, 
  propertyType: string, 
  yearBuilt?: number
): RequiredForm[] {
  // Normalize state code to uppercase
  const stateCode = state.toUpperCase();
  
  // Base forms that are always required
  const forms: RequiredForm[] = [
    {
      id: `${stateCode.toLowerCase()}-purchase-agreement`,
      name: `${STATE_NAMES[stateCode] || state} As-Is Purchase Agreement`,
      description: 'Main purchase agreement form',
      required: true,
    },
  ];

  // Lead-based paint disclosure (for homes built before 1978)
  if (yearBuilt && yearBuilt < 1978) {
    forms.push({
      id: 'lead-paint-disclosure',
      name: 'Lead-Based Paint Disclosure',
      description: 'Required for properties built before 1978',
      required: true,
    });
  }

  // State-specific forms
  switch (stateCode) {
    case 'CA':
      forms.push({
        id: 'ca-disclosure',
        name: 'California Residential Disclosure Statement',
        description: 'Required disclosure for California properties',
        required: true,
      });
      forms.push({
        id: 'ca-earthquake',
        name: 'Earthquake Hazards Disclosure',
        description: 'Required for properties in earthquake zones',
        required: true,
      });
      forms.push({
        id: 'ca-natural-hazard',
        name: 'Natural Hazard Disclosure Statement',
        description: 'Required for all California properties',
        required: true,
      });
      break;
    case 'TX':
      forms.push({
        id: 'tx-disclosure',
        name: 'Texas Property Disclosure',
        description: 'Required disclosure for Texas properties',
        required: true,
      });
      forms.push({
        id: 'tx-trec',
        name: 'TREC Information About Brokerage Services',
        description: 'Required for all Texas real estate transactions',
        required: true,
      });
      break;
    case 'FL':
      forms.push({
        id: 'fl-disclosure',
        name: 'Florida Property Disclosure',
        description: 'Required disclosure for Florida properties',
        required: true,
      });
      forms.push({
        id: 'fl-radon-gas',
        name: 'Florida Radon Gas Disclosure',
        description: 'Required for all Florida properties',
        required: true,
      });
      break;
    case 'NY':
      forms.push({
        id: 'ny-disclosure',
        name: 'New York Property Condition Disclosure',
        description: 'Required for New York properties',
        required: true,
      });
      break;
    // Add more states as needed
    default:
      // For states without specific requirements, add a generic disclosure
      if (DEV_MODE) {
        // In dev mode, add all possible forms for testing
        forms.push({
          id: 'dev-all-state-disclosure',
          name: 'All State Disclosure (DEV MODE)',
          description: 'Development testing form',
          required: true,
        });
      } else {
        forms.push({
          id: 'generic-disclosure',
          name: 'Property Disclosure Statement',
          description: 'General property disclosure',
          required: true,
        });
      }
  }

  // Property type specific forms
  if (propertyType === 'CONDO' || propertyType === 'TOWNHOUSE') {
    forms.push({
      id: 'hoa-disclosure',
      name: 'HOA Disclosure and Addendum',
      description: 'Required for properties with HOA',
      required: true,
    });
  }

  // Inspection waiver (optional)
  forms.push({
    id: 'inspection-waiver',
    name: 'Inspection Contingency Waiver',
    description: 'Optional waiver of inspection contingency',
    required: false,
  });

  return forms;
}

export function getFormsByAgreementType(
  agreementType: 'PARTIAL_FREE' | 'PARTIAL_PAID' | 'OFFICIAL',
  allForms: RequiredForm[]
): RequiredForm[] {
  switch (agreementType) {
    case 'PARTIAL_FREE':
      // Only include the main purchase agreement for free partial agreements
      return allForms.filter(form => form.id.includes('purchase-agreement'));
    case 'PARTIAL_PAID':
      // Include the main purchase agreement for paid partial agreements
      return allForms.filter(form => form.id.includes('purchase-agreement'));
    case 'OFFICIAL':
      // Include all required forms for official agreements
      return allForms.filter(form => form.required || DEV_MODE);
    default:
      return [];
  }
}


// Add PropertyType enum
export type PropertyType = 
  | 'SINGLE_FAMILY' 
  | 'CONDO' 
  | 'TOWNHOUSE' 
  | 'MULTI_FAMILY' 
  | 'LAND' 
  | 'COMMERCIAL';

// Update PropertyDetails to use PropertyType
export interface PropertyDetails {
  address: Address;
  listingPrice?: number;
  legalDescription?: string;
  propertyType?: PropertyType;
  yearBuilt?: number;
  bedrooms?: number;
  bathrooms?: number;
  squareFeet?: number;
  lotSize?: number;
  mls?: string;
}

// Update FormData to include developer mode
export interface FormData {
  step: number;
  propertyDetails?: PropertyDetails;
  buyerInfo?: BuyerInfo;
  offerDetails?: OfferDetails;
  agreementType?: 'PARTIAL_FREE' | 'PARTIAL_PAID' | 'OFFICIAL';
  requiredForms?: string[];
  email?: string;
  devMode?: boolean;
}

import jsPDF from 'jspdf';
import { FormData, PropertyDetails, BuyerInfo, OfferDetails, RequiredForm } from '@/app/types';
import { formatCurrency, formatDate } from './utils';
import { isDevMode } from './forms';

// Template loader for state-specific agreements
function getAgreementTemplate(stateCode: string): any {
  // In a real implementation, this would load templates from a database or file system
  // For now, we'll return a simple object with template sections
  const defaultTemplate = {
    title: 'AS-IS PURCHASE AGREEMENT',
    sections: [
      { name: 'PROPERTY', content: 'The real property and improvements located at: {address}' },
      { name: 'PURCHASE PRICE', content: 'Buyer agrees to pay: {price}' },
      { name: 'EARNEST MONEY', content: 'Buyer will deposit {earnestDeposit} within {earnestDepositDays} days' },
      { name: 'CLOSING', content: 'Closing shall occur on or before {closingDate}' },
      { name: 'INSPECTION', content: 'Buyer shall have {inspectionDays} days to conduct inspections' },
      { name: 'AS-IS CONDITION', content: 'Property is being sold in AS-IS condition with no warranties expressed or implied' },
    ]
  };
  
  // State-specific templates
  const templates = {
    'CA': {
      title: 'CALIFORNIA RESIDENTIAL PURCHASE AGREEMENT (AS-IS)',
      sections: [
        ...defaultTemplate.sections,
        { name: 'CALIFORNIA SPECIFIC', content: 'This agreement is subject to California law.' },
      ]
    },
    'TX': {
      title: 'TEXAS RESIDENTIAL PURCHASE AGREEMENT (AS-IS)',
      sections: [
        ...defaultTemplate.sections,
        { name: 'TEXAS SPECIFIC', content: 'This agreement is subject to Texas law.' },
      ]
    },
    // Add more state templates as needed
  };
  
  return templates[stateCode] || defaultTemplate;
}

export function generatePurchaseAgreement(
  formData: FormData,
  includeSellerName: boolean,
  includeForms: boolean
): jsPDF {
  const doc = new jsPDF();
  let yPosition = 20;
  
  // Get state code from property address
  const stateCode = formData.propertyDetails?.address?.state?.toUpperCase() || '';
  
  // Get the appropriate template
  const template = getAgreementTemplate(stateCode);

  // Header
  doc.setFontSize(20);
  doc.setFont('helvetica', 'bold');
  doc.text(template.title, 105, yPosition, { align: 'center' });
  yPosition += 15;

  // Add dev mode indicator if enabled
  if (isDevMode()) {
    doc.setFontSize(12);
    doc.setTextColor(255, 0, 0);
    doc.text('DEVELOPER TEST MODE - NOT FOR PRODUCTION USE', 105, yPosition, { align: 'center' });
    doc.setTextColor(0, 0, 0);
    yPosition += 10;
  }

  // Property Information
  doc.setFontSize(14);
  doc.text('PROPERTY INFORMATION', 20, yPosition);
  yPosition += 10;
  
  // Add template sections
  for (const section of template.sections) {
    doc.setFontSize(12);
    doc.setFont('helvetica', 'bold');
    doc.text(section.name, 20, yPosition);
    yPosition += 6;
    
    doc.setFont('helvetica', 'normal');
    // Replace placeholders with actual data
    let content = section.content
      .replace('{address}', formData.propertyDetails?.address?.fullAddress || '')
      .replace('{price}', formatCurrency(formData.offerDetails?.offerPrice || 0))
      .replace('{earnestDeposit}', formatCurrency(formData.offerDetails?.earnestDeposit || 0))
      .replace('{earnestDepositDays}', formData.offerDetails?.earnestDepositDays?.toString() || '3')
      .replace('{closingDate}', formData.offerDetails?.closingDate || 'TBD')
      .replace('{inspectionDays}', formData.offerDetails?.inspectionDays?.toString() || '10');
    
    doc.text(content, 20, yPosition);
    yPosition += 15;
  }
  
  // Add seller information if included
  if (includeSellerName) {
    doc.setFontSize(12);
    doc.setFont('helvetica', 'bold');
    doc.text('SELLER INFORMATION', 20, yPosition);
    yPosition += 6;
    
    doc.setFont('helvetica', 'normal');
    doc.text('Seller Name: ___________________________', 20, yPosition);
    yPosition += 15;
  } else {
    doc.setFontSize(12);
    doc.setFont('helvetica', 'bold');
    doc.text('SELLER INFORMATION', 20, yPosition);
    yPosition += 6;
    
    doc.setFont('helvetica', 'normal');
    doc.text('Seller Name: [TO BE ADDED UPON ACCEPTANCE]', 20, yPosition);
    yPosition += 15;
  }
  
  // Add buyer information
  doc.setFontSize(12);
  doc.setFont('helvetica', 'bold');
  doc.text('BUYER INFORMATION', 20, yPosition);
  yPosition += 6;
  
  doc.setFont('helvetica', 'normal');
  doc.text(`Buyer Name: ${formData.buyerInfo?.name || ''}`, 20, yPosition);
  yPosition += 6;
  doc.text(`Buyer Email: ${formData.buyerInfo?.email || ''}`, 20, yPosition);
  yPosition += 6;
  if (formData.buyerInfo?.phone) {
    doc.text(`Buyer Phone: ${formData.buyerInfo.phone}`, 20, yPosition);
    yPosition += 6;
  }
  
  // Add signature lines
  yPosition += 20;
  doc.line(20, yPosition, 100, yPosition);
  doc.text('Buyer Signature', 20, yPosition + 5);
  
  doc.line(120, yPosition, 190, yPosition);
  doc.text('Seller Signature', 120, yPosition + 5);
  
  return doc;
}

export function generateAdditionalForms(
  formData: FormData,
  requiredForms: RequiredForm[]
): jsPDF[] {
  // Generate additional forms based on requirements
  const documents: jsPDF[] = [];
  
  for (const form of requiredForms) {
    // Skip the main purchase agreement as it's handled separately
    if (form.id.includes('purchase-agreement')) continue;
    
    const doc = new jsPDF();
    let yPosition = 20;
    
    // Form header
    doc.setFontSize(18);
    doc.setFont('helvetica', 'bold');
    doc.text(form.name.toUpperCase(), 105, yPosition, { align: 'center' });
    yPosition += 10;
    
    // Form description
    doc.setFontSize(12);
    doc.setFont('helvetica', 'normal');
    doc.text(form.description, 105, yPosition, { align: 'center' });
    yPosition += 15;
    
    // Add dev mode indicator if enabled
    if (isDevMode()) {
      doc.setFontSize(12);
      doc.setTextColor(255, 0, 0);
      doc.text('DEVELOPER TEST MODE - NOT FOR PRODUCTION USE', 105, yPosition, { align: 'center' });
      doc.setTextColor(0, 0, 0);
      yPosition += 10;
    }
    
    // Form content would be added here based on the form type
    // For now, we'll add placeholder text
    doc.setFontSize(12);
    doc.text(`This is a placeholder for the ${form.name} form.`, 20, yPosition);
    yPosition += 10;
    doc.text(`Property Address: ${formData.propertyDetails?.address?.fullAddress || ''}`, 20, yPosition);
    yPosition += 10;
    
    // Add signature lines
    yPosition += 20;
    doc.line(20, yPosition, 100, yPosition);
    doc.text('Buyer Signature', 20, yPosition + 5);
    
    doc.line(120, yPosition, 190, yPosition);
    doc.text('Seller Signature', 120, yPosition + 5);
    
    documents.push(doc);
  }
  
  return documents;
}

export async function savePurchaseAgreement(
  formData: FormData,
  includeSellerName: boolean,
  includeForms: boolean
): Promise<string[]> {
  const mainDoc = generatePurchaseAgreement(formData, includeSellerName, includeForms);
  
  // Generate filename
  const timestamp = new Date().toISOString().slice(0, 10);
  const buyerName = formData.buyerInfo?.name.replace(/\s+/g,