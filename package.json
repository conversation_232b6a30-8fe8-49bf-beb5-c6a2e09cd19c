{"name": "realestate-agreement", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint", "postinstall": "prisma generate"}, "dependencies": {"@hookform/resolvers": "^3.3.4", "@prisma/client": "^5.10.2", "@radix-ui/react-checkbox": "^1.0.4", "@radix-ui/react-dialog": "^1.0.5", "@radix-ui/react-progress": "^1.0.3", "@radix-ui/react-select": "^2.0.0", "@stripe/stripe-js": "^3.0.6", "@supabase/supabase-js": "^2.39.7", "axios": "^1.6.7", "bcryptjs": "^2.4.3", "framer-motion": "^11.0.8", "html2canvas": "^1.4.1", "jose": "^5.2.2", "jspdf": "^3.0.1", "lucide-react": "^0.344.0", "next": "^14.2.29", "next-auth": "^4.24.6", "react": "^18", "react-dom": "^18", "react-hook-form": "^7.50.1", "stripe": "^14.19.0", "zod": "^3.22.4"}, "devDependencies": {"@types/bcryptjs": "^2.4.6", "@types/node": "^20", "@types/react": "^18", "@types/react-dom": "^18", "autoprefixer": "^10.0.1", "eslint": "^8", "eslint-config-next": "14.1.0", "postcss": "^8", "prisma": "^5.10.2", "tailwindcss": "^3.3.0", "typescript": "^5"}}