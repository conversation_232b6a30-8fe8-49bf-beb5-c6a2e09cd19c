generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
}

model User {
  id                String         @id @default(cuid())
  email             String         @unique
  password          String
  name              String?
  createdAt         DateTime       @default(now())
  updatedAt         DateTime       @updatedAt
  subscription      Subscription?
  agreements        Agreement[]
  drafts            Draft[]
}

model Subscription {
  id                String         @id @default(cuid())
  userId            String         @unique
  user              User           @relation(fields: [userId], references: [id])
  plan              SubscriptionPlan
  status            SubscriptionStatus @default(ACTIVE)
  currentPeriodEnd  DateTime
  officialUsed      Int            @default(0)
  unofficialUsed    Int            @default(0)
  stripeCustomerId  String?
  stripeSubscriptionId String?
  createdAt         DateTime       @default(now())
  updatedAt         DateTime       @updatedAt
}

model Agreement {
  id                String         @id @default(cuid())
  userId            String?
  user              User?          @relation(fields: [userId], references: [id])
  type              AgreementType
  propertyAddress   String
  propertyDetails   Json
  buyerInfo         Json
  sellerInfo        Json?
  offerDetails      Json
  forms             Json[]
  pdfUrl            String?
  status            AgreementStatus @default(DRAFT)
  createdAt         DateTime       @default(now())
  updatedAt         DateTime       @updatedAt
}

model Draft {
  id                String         @id @default(cuid())
  userId            String?
  user              User?          @relation(fields: [userId], references: [id])
  email             String?
  currentStep       Int            @default(1)
  data              Json
  expiresAt         DateTime
  createdAt         DateTime       @default(now())
  updatedAt         DateTime       @updatedAt
}

enum SubscriptionPlan {
  BASIC     // 25 official + 25 unofficial
  STANDARD  // 50 official + 50 unofficial
  PREMIUM   // 100 official + 100 unofficial
}

enum SubscriptionStatus {
  ACTIVE
  CANCELLED
  EXPIRED
}

enum AgreementType {
  PARTIAL_FREE      // No seller name, no forms
  PARTIAL_PAID      // With seller name, no forms
  OFFICIAL          // With seller name and all forms
}

enum AgreementStatus {
  DRAFT
  COMPLETED
  EXPIRED
}